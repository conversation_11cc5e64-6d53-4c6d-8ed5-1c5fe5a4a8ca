// Test to verify HTML email content
const fs = require('fs');
const path = require('path');

function testHtmlEmail() {
    console.log('🔍 Testing HTML email content...\n');
    
    try {
        // Load and process template like the service does
        const sharedStylesPath = path.join('src', 'mail', 'templates', 'shared-styles.css');
        const sharedStyles = fs.readFileSync(sharedStylesPath, 'utf8');
        
        const htmlPath = path.join('src', 'mail', 'templates', 'activation', 'sv.html');
        let htmlTemplate = fs.readFileSync(htmlPath, 'utf8');
        
        // Inject styles
        const styleRegex = /<style[^>]*>[\s\S]*?<\/style>/gi;
        const sharedStylesTag = `<style>\n${sharedStyles}\n    </style>`;
        htmlTemplate = htmlTemplate.replace(styleRegex, sharedStylesTag);
        
        // Replace variables
        const variables = {
            customerName: 'Test Användare',
            activationCode: 'SV123TEST'
        };
        
        Object.entries(variables).forEach(([key, value]) => {
            const placeholder = `{{${key}}}`;
            htmlTemplate = htmlTemplate.replace(new RegExp(placeholder, 'g'), value || '');
        });
        
        // Check for potential HTML issues
        console.log('📊 HTML VALIDATION CHECKS:');
        console.log(`   Has DOCTYPE: ${htmlTemplate.includes('<!DOCTYPE html>') ? '✅' : '❌'}`);
        console.log(`   Has html tag: ${htmlTemplate.includes('<html') ? '✅' : '❌'}`);
        console.log(`   Has head section: ${htmlTemplate.includes('<head>') ? '✅' : '❌'}`);
        console.log(`   Has body section: ${htmlTemplate.includes('<body>') ? '✅' : '❌'}`);
        console.log(`   Has CSS styles: ${htmlTemplate.includes('font-family') ? '✅' : '❌'}`);
        console.log(`   Has activation code: ${htmlTemplate.includes('SV123TEST') ? '✅' : '❌'}`);
        console.log(`   Has customer name: ${htmlTemplate.includes('Test Användare') ? '✅' : '❌'}`);
        console.log(`   Total length: ${htmlTemplate.length} characters\n`);
        
        // Check for common email HTML issues
        const hasInlineStyles = htmlTemplate.includes('style=');
        const hasExternalLinks = htmlTemplate.includes('href=');
        const hasImages = htmlTemplate.includes('<img');
        
        console.log('📧 EMAIL COMPATIBILITY CHECKS:');
        console.log(`   Has inline styles: ${hasInlineStyles ? '✅' : '❌'}`);
        console.log(`   Has external links: ${hasExternalLinks ? '✅' : '❌'}`);
        console.log(`   Has images: ${hasImages ? '❌ (Good - no images)' : '✅'}`);
        
        // Save processed HTML for inspection
        fs.writeFileSync('processed-email.html', htmlTemplate);
        console.log('\n💾 Processed HTML saved to: processed-email.html');
        console.log('   You can open this file in a browser to see how it looks\n');
        
        // Check if HTML might be too complex for some email clients
        const complexityScore = (htmlTemplate.match(/<[^>]+>/g) || []).length;
        console.log(`📈 HTML COMPLEXITY:`);
        console.log(`   Number of HTML tags: ${complexityScore}`);
        console.log(`   Complexity level: ${complexityScore > 50 ? 'High' : complexityScore > 20 ? 'Medium' : 'Low'}\n`);
        
        if (complexityScore > 50) {
            console.log('⚠️  WARNING: High HTML complexity might cause issues in some email clients');
        }
        
        console.log('✅ HTML email test completed successfully!');
        
    } catch (error) {
        console.error('❌ Error testing HTML email:', error.message);
    }
}

testHtmlEmail();
