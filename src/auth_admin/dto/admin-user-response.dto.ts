import { ApiProperty } from '@nestjs/swagger';
import { UserAdminRole } from '@prisma/client';

export class AdminUserDto {
  @ApiProperty({ description: 'The admin user ID' })
  id: string;

  @ApiProperty({ description: 'The admin user name' })
  name: string;

  @ApiProperty({ description: 'The admin user email' })
  email: string;

  @ApiProperty({
    description: 'The admin user role',
    enum: UserAdminRole,
    enumName: 'UserAdminRole',
  })
  role: UserAdminRole;
}

export class AdminUsersResponseDto {
  @ApiProperty({ description: 'Success status' })
  success: boolean;

  @ApiProperty({
    description: 'List of admin users',
    type: [AdminUserDto],
  })
  data: AdminUserDto[];

  @ApiProperty({ description: 'Response message' })
  message: string;
}
