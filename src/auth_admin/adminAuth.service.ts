import {
  Injectable,
  Logger,
  ConflictException,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { DatabaseService } from 'src/database/database.service';
import { LogInDto } from './dto/login.dto';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { UnauthorizedException } from '@nestjs/common/exceptions';
import { UserAdminRole } from '@prisma/client';
import { LoginResponseDto } from './dto/login-response.dto';
import { EMAIL_TEMPLATES, FRONTEND_ROUTES } from './constants';
import { MailService } from '../mail/mail.service';
import { ConfigService } from '@nestjs/config';
import {
  generateSessionToken,
  hashSessionToken,
  verifySessionToken,
} from './utils';
import * as moment from 'moment';

@Injectable()
export class AdminAuthService {
  private readonly logger = new Logger(AdminAuthService.name);
  constructor(
    private database: DatabaseService,
    private jwtService: JwtService,
    private mailService: MailService, // Add this line
    private readonly configService: ConfigService,
  ) {}

  async createVerificationToken(
    email: string,
    template:
      | typeof EMAIL_TEMPLATES.VERIFICATION
      | typeof EMAIL_TEMPLATES.PASSWORD_RESET,
    expiresInHours: number = 24,
  ): Promise<void> {
    const user = await this.database.user_login.findUnique({
      where: { email },
    });
    if (!user) {
      throw new NotFoundException('No user found with this email address');
    }

    // Delete any existing verification tokens for this email
    await this.database.verification_token.deleteMany({
      where: { identifier: email },
    });

    // Generate secure token
    const token = generateSessionToken();
    const hashedToken = hashSessionToken(token);

    // Set token expiration
    const expires = moment().clone().add(expiresInHours, 'hours').toISOString();
    // Save the verification token
    await this.database.verification_token.create({
      data: {
        identifier: email,
        token: hashedToken,
        expires,
      },
    });

    // Generate the appropriate URL based on the template type
    const frontendUrl = this.configService.get<string>('FRONTEND_URL');
    const link =
      template === EMAIL_TEMPLATES.PASSWORD_RESET
        ? `${frontendUrl}${FRONTEND_ROUTES.RESET_PASSWORD}?token=${token}`
        : `${process.env.APP_URL}/auth_admin/verify/${token}`;

    const name = user.name || email.split('@')[0];

    // Send email
    try {
      await this.mailService.sendMail(
        email,
        name,
        template.getSubject(),
        template.getText(link),
        template.getHTML(name, link),
      );
      this.logger.log(`Email sent successfully to ${email}`);
    } catch (error) {
      this.logger.error(`Failed to send email to ${email}:`, error);
      throw new Error('Failed to send email');
    }
  }

  async verifyEmail(token: string): Promise<boolean> {
    this.logger.debug(`Attempting to verify token: ${token}`);

    const verificationTokens = await this.database.verification_token.findMany({
      where: {
        expires: {
          gt: moment().toISOString(),
        },
      },
    });

    this.logger.debug(`Found ${verificationTokens.length} non-expired tokens`);

    const matchingToken = verificationTokens.find((vt) => {
      try {
        const isValid = verifySessionToken(token, vt.token);
        this.logger.debug(`Token comparison result: ${isValid}`);
        return isValid;
      } catch (error) {
        this.logger.error(`Error verifying token: ${error.message}`);
        return false;
      }
    });

    if (!matchingToken) {
      this.logger.warn('No matching token found');
      throw new NotFoundException('Invalid or expired verification token');
    }

    // Update user's email verification status
    await this.database.user_login.update({
      where: {
        email: matchingToken.identifier,
      },
      data: {
        emailVerified: moment().toISOString(),
      },
    });

    // Delete the used verification token
    await this.database.verification_token.delete({
      where: {
        id: matchingToken.id,
      },
    });

    return true;
  }

  async resendVerificationEmail(email: string): Promise<void> {
    const template = EMAIL_TEMPLATES.VERIFICATION;
    const user = await this.database.user_login.findUnique({
      where: { email },
    });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (user.emailVerified) {
      throw new ConflictException('Email is already verified');
    }

    // Create and send new verification token
    await this.createVerificationToken(email, template);
  }

  async login(loginDto: LogInDto): Promise<LoginResponseDto> {
    const { email, password } = loginDto;

    const user = await this.database.user_login.findFirst({
      where: { email },
    });

    if (!user) {
      throw new UnauthorizedException('Invalid email or password');
    }
    const isPasswordMatched = await bcrypt.compare(password, user.password);

    if (!isPasswordMatched) {
      throw new UnauthorizedException('Invalid email or password');
    }
    // Generate session token
    const sessionToken = generateSessionToken();
    const hashedToken = hashSessionToken(sessionToken);

    const session = await this.database.login_session.create({
      data: {
        sessionToken: hashedToken,
        userId: user.id,
        expires: moment().add(24, 'hours').toISOString(), // 24 hours from now
      },
    });

    const response: LoginResponseDto = {
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        emailVerified: user.emailVerified,
      },
      sessionToken,
      expiresAt: session.expires,
    };

    return response;
  }

  async registerAdmin(
    email: string,
    password: string,
    name: string,
    role: UserAdminRole,
  ) {
    const template = EMAIL_TEMPLATES.VERIFICATION;
    // Check if the email is already in use
    const existingUser = await this.database.user_login.findUnique({
      where: { email },
    });

    if (existingUser) {
      throw new ConflictException('Email already in use');
    }

    // Create the user_login
    const user = await this.database.user_login.create({
      data: {
        email,
        password,
        name,
        role,
        emailVerified: null,
      },
    });

    // Create the account
    await this.database.account.create({
      data: {
        userId: user.id,
        role,
        provider: 'credentials',
        providerAccountId: user.id,
      },
    });

    // Create and send verification token
    await this.createVerificationToken(email, template);

    return {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
      emailVerified: user.emailVerified,
    };
  }

  async createPasswordResetToken(email: string): Promise<void> {
    const template = EMAIL_TEMPLATES.PASSWORD_RESET;
    // Find the user
    const user = await this.database.user_login.findUnique({
      where: { email },
    });

    if (!user) {
      throw new NotFoundException('No user found with this email');
    }

    await this.createVerificationToken(
      email,
      template,
      2, // Password reset tokens expire in 1 hour
    );
  }

  async resetPassword(token: string, newPassword: string): Promise<void> {
    // Find the valid token
    const verificationTokens = await this.database.verification_token.findMany({
      where: {
        expires: {
          gt: moment().toISOString(),
        },
      },
    });

    const matchingToken = verificationTokens.find((vt) => {
      try {
        return verifySessionToken(token, vt.token);
      } catch (error) {
        this.logger.error(`Error verifying token: ${error.message}`);
        return false;
      }
    });

    if (!matchingToken) {
      throw new BadRequestException('Invalid or expired token');
    }

    // Hash the new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // Update the user's password
    await this.database.user_login.update({
      where: {
        email: matchingToken.identifier,
      },
      data: {
        password: hashedPassword,
      },
    });

    // Delete the used token
    await this.database.verification_token.delete({
      where: {
        id: matchingToken.id,
      },
    });
  }

  /**
   * Get admin users for assignment purposes
   * Only accessible by ADMIN, SUPERADMIN, or DEVELOPER roles
   */
  async getAdminUsers(requestingUserRole: UserAdminRole): Promise<
    {
      id: string;
      name: string;
      email: string;
      role: UserAdminRole;
    }[]
  > {
    // Check if requesting user has permission to view admin users
    const allowedRoles: UserAdminRole[] = [
      UserAdminRole.ADMIN,
      UserAdminRole.SUPERADMIN,
      UserAdminRole.SUBADMIN,
      UserAdminRole.DEVELOPER,
    ];

    if (!allowedRoles.includes(requestingUserRole)) {
      throw new UnauthorizedException(
        'Insufficient permissions to view admin users',
      );
    }

    // Define which roles can be assigned todos based on requesting user's role
    let assignableRoles: UserAdminRole[] = [];

    switch (requestingUserRole) {
      case UserAdminRole.SUPERADMIN:
        // SUPERADMIN can assign to all admin roles
        assignableRoles = [
          UserAdminRole.SUPERADMIN,
          UserAdminRole.ADMIN,
          UserAdminRole.SUBADMIN,
          UserAdminRole.DEVELOPER,
          UserAdminRole.MARKETING,
        ];
        break;
      case UserAdminRole.ADMIN:
        // ADMIN can assign to admin roles except SUPERADMIN
        assignableRoles = [
          UserAdminRole.ADMIN,
          UserAdminRole.SUBADMIN,
          UserAdminRole.DEVELOPER,
          UserAdminRole.MARKETING,
        ];
        break;
      case UserAdminRole.SUBADMIN:
        // DEVELOPER can assign to technical roles
        assignableRoles = [UserAdminRole.SUBADMIN, UserAdminRole.MARKETING];
        break;
      case UserAdminRole.DEVELOPER:
        // DEVELOPER can assign to technical roles
        assignableRoles = [
          UserAdminRole.DEVELOPER,
          UserAdminRole.ADMIN,
          UserAdminRole.SUBADMIN,
          UserAdminRole.MARKETING,
        ];
        break;
      default:
        throw new UnauthorizedException('Insufficient permissions');
    }

    const adminUsers = await this.database.user_login.findMany({
      where: {
        role: {
          in: assignableRoles,
        },
        emailVerified: {
          not: null, // Only include verified users
        },
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
      },
      orderBy: [{ role: 'asc' }, { name: 'asc' }],
    });

    return adminUsers;
  }
}
