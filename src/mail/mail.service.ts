import { Injectable } from '@nestjs/common';
const Mailjet = require('node-mailjet');

@Injectable()
export class MailService {
  private mailjet: any;

  constructor() {
    const apiKey: string = process.env.MJ_KEY || '';
    const apiSecret: string = process.env.MJ_SECRET || '';
    this.mailjet = Mailjet.apiConnect(apiKey, apiSecret);
  }

  getMailJet(): any {
    return this.mailjet;
  }

  async sendMail(
    email: string,
    name: string,
    subject: string,
    text: string,
    html: string,
    inlinedAttachments?: Array<{
      ContentID: string;
      ContentType: string;
      Base64Content: string;
    }>,
  ) {
    // Debug logging to check what's being sent
    console.log('[MailService Debug] Email parameters:');
    console.log(`  To: ${email} (${name})`);
    console.log(`  Subject: ${subject}`);
    console.log(`  Text length: ${text?.length || 0}`);
    console.log(`  HTML length: ${html?.length || 0}`);
    console.log(`  HTML has content: ${html && html.includes('<div') ? 'YES' : 'NO'}`);
    console.log(`  Text has content: ${text && text.length > 50 ? 'YES' : 'NO'}`);

    let message = {
      From: {
        Email: '<EMAIL>',
        Name: 'IMVI',
      },
      To: [
        {
          Email: email,
          Name: name,
        },
      ],
      Subject: subject,
      TextPart: text,
      HTMLPart: html,
      // Ensure HTML is prioritized over text
      Headers: {
        'Content-Type': 'text/html; charset=UTF-8'
      }
    };

    if (inlinedAttachments) {
      message['InlinedAttachments'] = inlinedAttachments;
    }
    const Messages = [message];

    // Log the complete message being sent to Mailjet
    console.log('[MailService Debug] Complete Mailjet message:');
    console.log(JSON.stringify(message, null, 2));

    try {
      const request = await this.mailjet
        .post('send', { version: 'v3.1' })
        .request({
          Messages: Messages,
        });

      const result = await request;
      console.log('[MailService Debug] Mailjet response:');
      console.log(result.body);
      return result.body;
    } catch (error) {
      console.log('Error sending email:', error);
      throw new Error('Mail sending failed');
    }
  }
}
