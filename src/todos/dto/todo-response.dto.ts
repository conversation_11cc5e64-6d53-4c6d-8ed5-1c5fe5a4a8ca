import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TodoStatus, TodoPriority } from '@prisma/client';

export class TodoResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the todo',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Todo title',
    example: 'Review quarterly sales report',
  })
  title: string;

  @ApiPropertyOptional({
    description: 'Detailed description of the todo',
    example: 'Review and analyze Q4 sales performance',
  })
  description?: string;

  @ApiProperty({
    enum: TodoStatus,
    description: 'Current status of the todo',
    example: TodoStatus.PENDING,
  })
  status: TodoStatus;

  @ApiProperty({
    enum: TodoPriority,
    description: 'Priority level of the todo',
    example: TodoPriority.HIGH,
  })
  priority: TodoPriority;

  @ApiPropertyOptional({
    description: 'Due date for the todo',
    example: '2024-12-31T23:59:59.000Z',
  })
  due_date?: Date;

  @ApiPropertyOptional({
    description: 'Reminder time for the todo',
    example: '2024-12-30T09:00:00.000Z',
  })
  reminder_time?: Date;

  @ApiPropertyOptional({
    description: 'Completion timestamp',
    example: '2024-12-25T14:30:00.000Z',
  })
  completed_at?: Date;

  @ApiProperty({
    description: 'ID of the user who created the todo',
    example: 'user123',
  })
  created_by: string;

  @ApiPropertyOptional({
    description: 'Name of the user who created the todo',
    example: 'John Doe',
  })
  created_by_name?: string;

  @ApiPropertyOptional({
    description: 'ID of the user assigned to the todo',
    example: 'user456',
  })
  assigned_to?: string;

  @ApiPropertyOptional({
    description: 'Name of the user assigned to the todo',
    example: 'Jane Smith',
  })
  assigned_to_name?: string;

  @ApiPropertyOptional({
    description: 'ID of the user who last updated the todo',
    example: 'user789',
  })
  updated_by?: string;

  @ApiPropertyOptional({
    description: 'Name of the user who last updated the todo',
    example: 'Bob Johnson',
  })
  updated_by_name?: string;

  @ApiPropertyOptional({
    description: 'Whether the todo is private to the assignee',
    default: false,
  })
  is_private?: boolean = false;

  @ApiPropertyOptional({
    description: 'Array of tags for categorization',
    example: ['sales', 'quarterly', 'urgent'],
    type: [String],
  })
  tags?: string[];

  @ApiPropertyOptional({
    description: 'Additional metadata',
    example: { category: 'business', department: 'sales' },
  })
  metadata?: Record<string, any>;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-12-20T10:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-12-21T15:30:00.000Z',
  })
  updatedAt: Date;
}
