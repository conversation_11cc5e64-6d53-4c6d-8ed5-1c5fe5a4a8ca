import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEnum,
  IsOptional,
  IsString,
  IsDateString,
  IsNumberString,
} from 'class-validator';
import { TodoStatus, TodoPriority } from '@prisma/client';

export class TodoQueryDto {
  @ApiPropertyOptional({
    description: 'Number of todos to return',
    example: '20',
    default: '50',
  })
  @IsNumberString()
  @IsOptional()
  limit?: string = '50';

  @ApiPropertyOptional({
    description: 'Number of todos to skip',
    example: '0',
    default: '0',
  })
  @IsNumberString()
  @IsOptional()
  offset?: string = '0';

  @ApiPropertyOptional({
    enum: TodoStatus,
    description: 'Filter by todo status',
    example: TodoStatus.PENDING,
  })
  @IsEnum(TodoStatus)
  @IsOptional()
  status?: TodoStatus;

  @ApiPropertyOptional({
    enum: TodoPriority,
    description: 'Filter by todo priority',
    example: TodoPriority.HIGH,
  })
  @IsEnum(TodoPriority)
  @IsOptional()
  priority?: TodoPriority;

  @ApiPropertyOptional({
    description: 'Filter by creator admin user ID',
    example: 'clm123abc456def789',
  })
  @IsString()
  @IsOptional()
  created_by?: string;

  @ApiPropertyOptional({
    description: 'Filter by assigned admin user ID',
    example: 'clm123abc456def789',
  })
  @IsString()
  @IsOptional()
  assigned_to?: string;

  @ApiPropertyOptional({
    description: 'Filter todos due after this date (ISO string)',
    example: '2024-12-01T00:00:00.000Z',
  })
  @IsDateString()
  @IsOptional()
  due_after?: string;

  @ApiPropertyOptional({
    description: 'Filter todos due before this date (ISO string)',
    example: '2024-12-31T23:59:59.000Z',
  })
  @IsDateString()
  @IsOptional()
  due_before?: string;

  @ApiPropertyOptional({
    description: 'Filter by tags (comma-separated)',
    example: 'sales,urgent,quarterly',
  })
  @IsString()
  @IsOptional()
  tags?: string;

  @ApiPropertyOptional({
    description: 'Search in title and description',
    example: 'sales report',
  })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiPropertyOptional({
    description: 'Include only overdue todos',
    example: 'true',
  })
  @IsString()
  @IsOptional()
  overdue_only?: string;

  @ApiPropertyOptional({
    description: 'Sort field',
    example: 'due_date',
    enum: ['createdAt', 'updatedAt', 'due_date', 'priority', 'title'],
  })
  @IsString()
  @IsOptional()
  sort_by?: string = 'createdAt';

  @ApiPropertyOptional({
    description: 'Sort order',
    example: 'desc',
    enum: ['asc', 'desc'],
  })
  @IsString()
  @IsOptional()
  sort_order?: string = 'desc';

  @ApiPropertyOptional({
    description: 'User ID for private todo filtering',
    example: 'clm123abc456def789',
  })
  @IsString()
  @IsOptional()
  userId?: string;
}
