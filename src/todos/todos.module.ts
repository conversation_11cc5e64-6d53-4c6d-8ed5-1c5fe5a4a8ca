import { Modu<PERSON> } from '@nestjs/common';
import { TodosService } from './todos.service';
import { TodosController } from './todos.controller';
import { DatabaseModule } from 'src/database/database.module';
import { PurchaseNotificationModule } from '../purchases/notifications/notification.module';
import { MailModule } from '../mail/mail.module';

@Module({
  controllers: [TodosController],
  providers: [TodosService],
  imports: [DatabaseModule, PurchaseNotificationModule, MailModule],
  exports: [TodosService],
})
export class TodosModule {}
