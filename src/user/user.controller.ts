import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Patch,
  Post,
  Put,
  Query,
  Res,
} from '@nestjs/common';
import { ApiBody, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { UserService } from './user.service';
import { LinkGuardianAccountDto } from './dto/link-guardian-account';
import { ResetPasswordDto } from './dto/reset-password';
import { LinkUserToStudentDto } from './dto/link-user-to-student.dto';
import { UpdateDeviceDataDto } from './dto/update-device-data.dto';
import { TrainingConfigDto } from './dto/training-config.dto';
import { RecoverUsernameDto } from './dto/recover-username-dto';
import { CreateUserDto } from './dto/create-user-no-uuid';

@ApiTags('user')
@Controller('user')
export class UserController {
  constructor(private userService: UserService) {}

  @Get('/check-subscription')
  @ApiOperation({
    summary: 'Check User Subscription',
    description:
      'Checks if a user with a given UUID has an active subscription.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Subscription status retrieved successfully.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found.',
  })
  async checkSubscription(@Query('uuid') uuid: string) {
    try {
      const { hasSubscription } =
        await this.userService.checkUserSubscription(uuid);
      return { uuid, hasSubscription };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.NOT_FOUND);
    }
  }

  @Get('/check-is-student')
  @ApiOperation({
    summary: 'Check if User is a Student',
    description: 'Checks if a user with a given UUID is a student.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User is a student.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found or not a student.',
  })
  async checkIsStudent(@Query('uuid') uuid: string) {
    try {
      const { isStudent } = await this.userService.checkIsStudent(uuid);
      return { isStudent };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.NOT_FOUND);
    }
  }

  @Post('/submit-survey')
  @ApiOperation({
    summary: 'Submit Survey',
    description: 'Submits survey responses for a given user.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Survey submitted successfully.',
  })
  @ApiBody({
    description: 'Survey submission data',
  })
  async submitSurvey(
    @Body()
    body: {
      userId: number;
      questionIndex: number;
      answerArray: number[];
    },
  ) {
    return this.userService.submitSurvey(
      body.userId,
      body.questionIndex,
      body.answerArray,
    );
  }

  @Get('/check-user-answered-initial-survey')
  @ApiOperation({
    summary: 'Check User Answered Initial Survey',
    description: 'Checks if a user has answered the initial survey questions.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Initial survey status retrieved successfully.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found.',
  })
  async checkUserAnsweredInitialSurvey(@Query('uuid') uuid: string) {
    try {
      const { hasAnswered } =
        await this.userService.hasUserAnsweredSurvey(uuid);
      return { hasAnswered };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.NOT_FOUND);
    }
  }

  @Post('/link-guardian-account')
  @ApiOperation({
    summary: 'Link Guardian Account',
    description: 'Links a child account with a guardian account.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Guardian account linked successfully.',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Failed to link guardian account.',
  })
  @ApiBody({
    description: 'Data required to link guardian account',
    type: LinkGuardianAccountDto,
  })
  async linkGuardianAccount(
    @Body() linkGuardianAccountDto: LinkGuardianAccountDto,
  ): Promise<{ success: boolean }> {
    try {
      const { guardianAccountLinked } =
        await this.userService.linkGuardianAccount(linkGuardianAccountDto);
      return { success: true };
    } catch (error) {
      throw new HttpException(
        'Failed to link guardian account',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('/reset-child-account-password')
  async resetChildAccountPassword(
    @Body() resetPasswordDto: ResetPasswordDto,
  ): Promise<{ success: boolean }> {
    try {
      const { success } =
        await this.userService.resetChildAccountPassword(resetPasswordDto);
      return { success: success };
    } catch (error) {}
  }

  @Post('/send-reset-password-to-admin')
  async sendResetPasswordToAdmin(
    @Body() resetPasswordDto: ResetPasswordDto,
  ): Promise<{ success: boolean }> {
    try {
      const { success } =
        await this.userService.sendResetPasswordToAdmin(resetPasswordDto);
      return { success: success };
    } catch (error) {}
  }

  @Delete('/delete')
  @ApiOperation({
    summary: 'Deletes User',
    description: 'Deletes a user account based on the provided UUID. ',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User account disabled successfully.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error occurred.',
  })
  @ApiBody({
    description: 'UUID of the user to be disabled',
    schema: {
      type: 'object',
      properties: {
        uuid: {
          type: 'string',
          description: 'The unique identifier of the user to disable.',
        },
      },
    },
  })
  async disableUser(@Body('uuid') uuid: string): Promise<any> {
    try {
      const result = await this.userService.disableFirebaseUser(uuid);
      return result;
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Get('/parent-info')
  @ApiOperation({
    summary: 'Get Parent Information',
    description: 'Retrieves parent information for a given user UUID.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Parent information retrieved successfully.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User or parent information not found.',
  })
  async getParentInfo(@Query('uuid') uuid: string) {
    try {
      const parentInfo = await this.userService.getParentInfoByUserUuid(uuid);
      return parentInfo;
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.NOT_FOUND);
    }
  }

  @Post('/link-to-student')
  @ApiOperation({
    summary: 'Link User to Student',
    description: 'Links a user to a student in the student table.',
  })
  @ApiBody({
    description: 'The user ID and student ID to link',
    type: LinkUserToStudentDto,
  })
  @ApiResponse({
    status: 200,
    description: 'User successfully linked to student.',
  })
  @ApiResponse({
    status: 404,
    description: 'Student or User not found.',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error.',
  })
  async linkUserToStudent(
    @Body() linkUserToStudentDto: LinkUserToStudentDto,
  ): Promise<{ message: string }> {
    try {
      await this.userService.linkUserToStudent(linkUserToStudentDto);
      return { message: 'User successfully linked to student.' };
    } catch (error) {
      let status = HttpStatus.INTERNAL_SERVER_ERROR;
      if (error.message.includes('not found')) {
        status = HttpStatus.NOT_FOUND;
      }
      throw new HttpException(
        {
          status: status,
          error: error.message,
        },
        status,
      );
    }
  }

  @Get('/get-additional-info')
  @ApiOperation({
    summary: 'Get User additional info',
    description: 'Retrieves the additional info for a given user UUID',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User additional info retrieved successfully.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User or additional information not found.',
  })
  async getAditionalInfo(@Query('uuid') uuid: string) {
    try {
      const userAddInfo =
        await this.userService.getAdditionalInfoByUserUUID(uuid);
      return userAddInfo;
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.NOT_FOUND);
    }
  }
  @Get('/all-users')
  @ApiOperation({
    summary: 'Get All Users',
    description:
      'Retrieves all users and their additional information with pagination and search support.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Users retrieved successfully.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error occurred.',
  })
  @Get('/all-users')
  async getAllUsers(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 50,
    @Query('search') search?: string,
    @Query('sortBy') sortBy: string = 'registered_on',
    @Query('sortOrder') sortOrder: string = 'asc',
    @Query('filterType') filterType: string = 'all',
  ): Promise<any> {
    const sanitizedSearch = this.userService.sanitizeSearch(search);
    const { users, total } = await this.userService.getAllUsers(
      page,
      limit,
      sanitizedSearch,
      sortBy,
      sortOrder,
      filterType,
    );
    return { users, total };
  }

  @Put('/:user_id/training_session')
  @ApiOperation({ summary: 'Update Training Session Data' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Training session data updated successfully.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Error updating training session data.',
  })
  async updateTrainingSession(
    @Param('user_id') userId: string,
    @Body()
    trainingData: {
      sessionNumber: number;
      sessionDuration: number;
      startTime: string;
      type: string;
      streamingSource?: string;
      speed: number;
      pendlumLength: number;
      offset: number;
      oscillationTime: number;
      isMigration: boolean;
      deviceId?: number;
    },
  ) {
    try {
      // Convert startTime from string to Date object
      const startTime = new Date(trainingData.startTime);

      if (isNaN(startTime.getTime())) {
        throw new Error('Invalid startTime format');
      }

      const result = await this.userService.saveTrainingSessionData({
        sessionNumber: trainingData.sessionNumber,
        sessionDuration: trainingData.sessionDuration,
        startTime,
        userId,
        type: trainingData.type,
        streamingSource: trainingData.streamingSource,
        speed: trainingData.speed,
        pendlumLength: trainingData.pendlumLength,
        offset: trainingData.offset,
        oscillationTime: trainingData.oscillationTime,
        isMigration: trainingData.isMigration,
        deviceId: trainingData.deviceId,
      });

      return { message: result };
    } catch (error) {
      console.error('Error updating training session data:', error);
      throw new HttpException(
        'Internal Server Error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/:user_id/training_session_data')
  @ApiOperation({ summary: 'Get Training Session Data' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Training session data retrieved successfully.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found or no training session data available.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error occurred.',
  })
  async getTrainingSessionData(@Param('user_id') userId: string) {
    try {
      const result = await this.userService.getTrainingSessionData(userId);

      if (!result || result.length === 0) {
        throw new HttpException(
          'No training session data found',
          HttpStatus.NOT_FOUND,
        );
      }

      return result;
    } catch (error) {
      console.error('Error fetching training session data:', error);
      throw new HttpException(
        'Internal Server Error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put('/:user_id/calibration_data')
  @ApiOperation({ summary: 'Update Calibration Data' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Calibration data saved successfully.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Error saving calibration data.',
  })
  async updateCalibrationData(
    @Param('user_id') userId: string,
    @Body()
    calibrationData: { array: number[]; datetime: string; deviceId?: number },
  ) {
    try {
      const { array, datetime, deviceId } = calibrationData;

      // Validate datetime format
      const calibrationDate = new Date(datetime);
      if (isNaN(calibrationDate.getTime())) {
        throw new Error('Invalid datetime format');
      }

      const result = await this.userService.saveCalibrationData({
        array,
        userId,
        datetime,
        deviceId,
      });

      return { message: result };
    } catch (error) {
      console.error('Error saving calibration data:', error);
      throw new HttpException(
        'Internal Server Error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/:user_id/calibration_data')
  @ApiOperation({
    summary: 'Get Calibration Data',
    description: 'Retrieves calibration data for a user by UUID.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Calibration data retrieved successfully.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error.',
  })
  async getCalibrationData(@Param('user_id') userId: string) {
    try {
      const calibrationData =
        await this.userService.getCalibrationDataByUserUuid(userId);
      return calibrationData;
    } catch (error) {
      console.error('Error fetching calibration data:', error.message);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Put('/:user_id/valid_until')
  @ApiOperation({ summary: 'Update ValidUntil Date' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'ValidUntil date saved successfully.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Error saving ValidUntil date.',
  })
  async updateValidUntilDate(
    @Param('user_id') userId: string,
    @Body() validUntilData: { validUntil: string },
  ) {
    try {
      const { validUntil } = validUntilData;

      // Validate the date format
      const validUntilDate = new Date(validUntil);
      if (isNaN(validUntilDate.getTime())) {
        throw new Error('Invalid date format');
      }

      const result = await this.userService.saveValidUntilDate({
        userId,
        validUntil: validUntilDate,
      });

      return { message: result };
    } catch (error) {
      console.error('Error saving ValidUntil date:', error);
      throw new HttpException(
        'Internal Server Error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
  @Put('/:user_id/change-flag')
  async updateChangeFlag(
    @Param('user_id') userId: string,
    @Body() { changeFlag }: { changeFlag: boolean },
  ): Promise<{ message: string }> {
    try {
      const result = await this.userService.updateChangeFlag(
        userId,
        changeFlag,
      );
      return { message: result };
    } catch (error) {
      console.error('Error updating change flag:', error);
      throw new HttpException(
        'Internal Server Error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
  @Put('/:user_id/read-calibration-from-backend')
  @ApiOperation({ summary: 'Update ReadCalibrationFromBackend Flag' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'ReadCalibrationFromBackend flag updated successfully.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Error updating ReadCalibrationFromBackend flag.',
  })
  async updateReadCalibrationFromBackend(
    @Param('user_id') userId: string,
    @Body()
    { readCalibrationFromBackend }: { readCalibrationFromBackend: boolean },
  ): Promise<{ message: string }> {
    try {
      const result = await this.userService.updateReadCalibrationFromBackend(
        userId,
        readCalibrationFromBackend,
      );
      return { message: result };
    } catch (error) {
      console.error('Error updating ReadCalibrationFromBackend flag:', error);
      throw new HttpException(
        'Internal Server Error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
  @Put('/:user_id/update-flag')
  @ApiOperation({ summary: 'Update Change Flag' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Flag updated successfully.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Error updating flag.',
  })
  async updateFlag(
    @Param('user_id') userId: string,
    @Body() { flag }: { flag: boolean },
  ): Promise<{ message: string }> {
    try {
      const result = await this.userService.updateUserFlag(userId, flag);
      return { message: result };
    } catch (error) {
      console.error('Error updating flag:', error.message);
      throw new HttpException(
        'Internal Server Error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put('/:user_id/clear_performed_test_flag')
  @ApiOperation({ summary: 'Reset performed_test_today flag for a user' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Performed test flag reset successfully.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found or no additional info available.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error occurred.',
  })
  async clearPerformedTestFlag(@Param('user_id') userUUID: string) {
    try {
      const message = await this.userService.resetPerformedTestFlag(userUUID);
      return { message };
    } catch (error) {
      console.error(
        `Error resetting performed_test_today flag for user ${userUUID}:`,
        error,
      );

      throw new HttpException(
        error.message || 'Internal Server Error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put('/:user_id/update-field')
  @ApiOperation({ summary: 'Update any field in user or user_additional_info' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Field updated successfully.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found or invalid field.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error occurred.',
  })
  async updateUserField(
    @Param('user_id') userUUID: string,
    @Body() updateData: { field: string; value: any },
  ): Promise<{ message: string }> {
    try {
      const { field, value } = updateData;
      const message = await this.userService.updateUserField(
        userUUID,
        field,
        value,
      );
      return { message };
    } catch (error) {
      console.error(
        `Error updating field '${updateData.field}' for user ${userUUID}:`,
        error,
      );
      throw new HttpException(
        error.message || 'Internal Server Error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put('/:user_id/update-device-data')
  @ApiOperation({ summary: 'Update device data for a user' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Device data updated successfully.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found or invalid data.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error occurred.',
  })
  async updateDeviceData(
    @Param('user_id') userUuid: string,
    @Body() updateData: UpdateDeviceDataDto,
  ): Promise<{ message: string; deviceData: any }> {
    try {
      const result = await this.userService.updateDeviceData(
        userUuid,
        updateData,
      );
      return result;
    } catch (error) {
      console.error(`Error updating device data for user ${userUuid}:`, error);
      throw new HttpException(
        error.message || 'Internal Server Error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/teachers')
  @ApiOperation({ summary: 'Get teachers for a student' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Teachers retrieved successfully.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Student not found or no teachers associated.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error occurred.',
  })
  async getTeachersForStudent(@Query('uuid') studentUuid: string): Promise<{
    teachers: Array<{
      firstName: string;
      lastName: string;
      teacher_uuid: string;
    }>;
  }> {
    try {
      const teachers =
        await this.userService.getTeachersForStudent(studentUuid);
      return { teachers };
    } catch (error) {
      console.error(
        `Error retrieving teachers for student ${studentUuid}:`,
        error,
      );
      throw new HttpException(
        error.message || 'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('/recover-username')
  @ApiOperation({ summary: 'Recover Username' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Username recovered successfully.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Error recovering username.',
  })
  async recoverUsername(
    @Body() recoverUsernameDto: RecoverUsernameDto,
  ): Promise<{ message: string; success?: boolean }> {
    try {
      const success =
        await this.userService.recoverUsername(recoverUsernameDto);
      return { message: 'Username recovered successfully.', success };
    } catch (error) {
      console.error('Error recovering username:', error);
      return { message: 'Failed to recover username', success: false };
    }
  }
  @Post('create-user-no-uuid')
  async createUser(@Body() createUserDto: CreateUserDto) {
    const user = await this.userService.createUser(createUserDto);
    return {
      message: 'User created successfully',
      data: user,
    };
  }
  @Get('/get-survey-answers')
  @ApiOperation({ summary: 'Get Survey Answers' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Survey answers retrieved successfully.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Error retrieving survey answers.',
  })
  async getSurveyAnswers(
    @Query('uuid') uuid: string,
  ): Promise<{ message: string; success: boolean; data?: any }> {
    return await this.userService.getSurveyAnswersByUuid(uuid);
  }

  @Get('/get-training-questions')
  @ApiOperation({ summary: 'Get Training Questions Data' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Training questions retrieved successfully.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Error retrieving training questions.',
  })
  async getTrainingQuestions(
    @Query('uuid') uuid: string,
  ): Promise<{ message: string; success: boolean; data?: any }> {
    return await this.userService.getTrainingQuestions(uuid);
  }

  @Get('/get-user-by-email')
  @ApiOperation({
    summary: 'Get User ID and UUID by Email',
    description: 'Retrieves user_id and uuid for a given email address',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User information retrieved successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error occurred',
  })
  async getUserByEmail(@Query('email') email: string) {
    try {
      const result = await this.userService.getUserByEmail(email);
      if (!result) {
        throw new HttpException('User not found', HttpStatus.NOT_FOUND);
      }
      return result;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Get('/pd/:userUuid')
  async readPd(@Param('userUuid') userUuid: string) {
    const pd = await this.userService.getPhysicalDistance(userUuid);
    return { userUuid, physicalDistance: pd };
  }

  @Patch('/pd/:userUuid')
  async updatePd(
    @Param('userUuid') userUuid: string,
    @Body('physicalDistance') pd: number,
  ) {
    await this.userService.setPhysicalDistance(userUuid, pd);
    return { userUuid, PhysicalDistance: pd };
  }
  
  @Post('/:user_uuid/training-config/:date')
  @ApiOperation({ summary: 'Add New Training Config' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Training config added successfully.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Error adding training config.',
  })
  async addTrainingConfig(
    @Param('user_uuid') userUUID: string,
    @Param('date') date: string,
    @Body() trainingConfigDto: TrainingConfigDto,
  ): Promise<{ message: string }> {
    try {
      const result = await this.userService.addTrainingConfig(
        userUUID,
        trainingConfigDto,
        date
      );
      return { message: result };
    } catch (error) {
      console.error('Error adding training config:', error);
      throw new HttpException(
        'Internal Server Error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/:user_uuid/training-config/:date')
  @ApiOperation({ summary: 'Fetch Training Config' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Training config fetched successfully.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Error fetching training config.',
  })
  async fetchTrainingConfig(
    @Param('user_uuid') userUUID: string,
    @Param('date') date: string,
  ): Promise<{ training_config: {} }> {
    try {
      const result = await this.userService.fetchTrainingConfig(
        userUUID, date
      );
      return { training_config: result };
    } catch (error) {
      console.error('Error fetching training config:', error);
      throw new HttpException(
        'Internal Server Error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/:user_uuid/training-config')
  @ApiOperation({ summary: 'Fetch Training Config History' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Training config fetched successfully.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Error fetching training config.',
  })
  async fetchTrainingConfigHistory(
    @Param('user_uuid') userUUID: string,
  ): Promise<{ training_config: {} }> {
    try {
      const result = await this.userService.fetchTrainingConfigHistory(
        userUUID
      );
      return { training_config: result };
    } catch (error) {
      console.error('Error fetching training config:', error);
      throw new HttpException(
        'Internal Server Error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
