import { initializeApp, cert } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import { getAuth } from 'firebase-admin/auth';
import { getMessaging } from 'firebase-admin/messaging';

import { Injectable, OnModuleInit } from '@nestjs/common';

@Injectable()
export class FirebaseService implements OnModuleInit {
  private firestore: any;
  private auth: any;
  private messaging: any;
  private tokenAuth: any;

  onModuleInit() {
    const imviConfig = JSON.parse(process.env.IMVI_FIREBASE_CONFIG);

    let imviApp = initializeApp(imviConfig);

    this.firestore = getFirestore(imviApp);
    this.auth = getAuth(imviApp);
    this.messaging = getMessaging(imviApp);

    const imviTokenConfig = JSON.parse(process.env.TOKEN_APP_CONFIG);
    let tokenApp = initializeApp({credential: cert(imviTokenConfig)}, 'tokenApp');
    this.tokenAuth = getAuth(tokenApp);
  }

  getTokenAuth(): any {
    return this.tokenAuth;
  }

  getFirestore(): any {
    return this.firestore;
  }

  getAuth(): any {
    return this.auth;
  }

  getMessaging(): any {
    return this.messaging;
  }

  async disableUser(uuid: string): Promise<void> {
    try {
      await this.auth.updateUser(uuid, { disabled: true });
      console.log(`Successfully disabled user with UUID: ${uuid}`);
    } catch (error) {
      console.error(`Error disabling user with UUID: ${uuid}`, error);
      throw error;
    }
  }

  // New: Verifying a Firebase Auth token
  async verifyToken(token: string): Promise<any> {
    try {
      return await this.auth.verifyIdToken(token);
    } catch (error) {
      console.error('Error verifying Firebase token', error);
      throw new Error('Unauthorized');
    }
  }
}
