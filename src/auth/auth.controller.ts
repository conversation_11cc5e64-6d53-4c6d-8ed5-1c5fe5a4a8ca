import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Post,
  UseFilters,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { SignUpDto } from './dto/sign-up.dto';
import { LoginLicenseDto } from './dto/login-license-code.dto';
import { LoginUserDto } from 'src/mapcog/dto/login-user-dto';
import { ApiBody, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AllExceptionsFilter } from 'src/expcetions-filter';

@UseFilters(AllExceptionsFilter)
@Controller('auth')
@ApiTags('auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @Post('/signup')
  @ApiOperation({
    summary: 'User Sign-Up',
    description: 'Registers a new user with the provided details.',
  })
  @ApiBody({
    description: 'The user sign-up information',
    type: SignUpDto,
  })
  @ApiResponse({
    status: 201,
    description: 'The user has been successfully created.',
  })
  @ApiResponse({
    status: 400,
    description: 'A user with this UUID already exists.',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error.',
  })
  async signUp(@Body() signUpDto: SignUpDto): Promise<{ user: any }> {
    try {
      return await this.authService.signUp(signUpDto);
    } catch (error) {
      let status = HttpStatus.INTERNAL_SERVER_ERROR;
      if (error.message.includes('UUID already exists')) {
        status = HttpStatus.BAD_REQUEST;
      }
      throw new HttpException(
        {
          status: status,
          error: error.message,
        },
        status,
      );
    }
  }

  // write a login endpoint that received a license code  of 4 characters
  @Post('/login')
  @ApiOperation({
    summary: 'User Login',
    description: 'Logs in a user with the provided license code.',
  })
  @ApiBody({
    description: 'The user login information',
    type: LoginLicenseDto,
  })
  @ApiResponse({
    status: 200,
    description: 'The user has been successfully logged in.',
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid license code or student / user not found.',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error.',
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  async login(@Body() license: LoginLicenseDto): Promise<{ token: string }> {
    try {
      return await this.authService.login(license);
    } catch (error) {
      let status = HttpStatus.INTERNAL_SERVER_ERROR;
      if (error.message.includes('Invalid license code')) {
        status = HttpStatus.BAD_REQUEST;
      }
      throw new HttpException(
        {
          status: status,
          error: error.message,
        },
        status,
      );
    }
  }

}
