import { Injectable } from '@nestjs/common';
import { DatabaseService } from 'src/database/database.service';

export interface CreateActivityDto {
  admin_id?: string | null;
  action: string;
  entity?: string;
  entity_id?: string | null;
  details?: Record<string, any>;
  ip_address?: string | null;
}

@Injectable()
export class AdminActivitiesService {
  constructor(private database: DatabaseService) {}

  async logActivity(data: CreateActivityDto) {
    return this.database.admin_activity.create({
      data: {
        admin_id: data.admin_id,
        action: data.action,
        entity: data.entity,
        entity_id: data.entity_id,
        details: data.details,
      },
    });
  }

  async getActivities(params: {
    skip?: number;
    take?: number;
    admin_id?: string;
    action?: string;
    startDate?: Date;
    endDate?: Date;
  }) {
    const { skip, take, admin_id, action, startDate, endDate } = params;

    return this.database.admin_activity.findMany({
      skip,
      take,
      where: {
        admin_id: admin_id,
        action: action,
        created_at: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        admin: {
          select: {
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        created_at: 'desc',
      },
    });
  }
}
