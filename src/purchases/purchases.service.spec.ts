import { Test, TestingModule } from '@nestjs/testing';
import { PurchasesService } from './purchases.service';
import { ShopifyOrder } from './interfaces/shopify.interface';

describe('PurchasesService', () => {
  let service: PurchasesService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: PurchasesService,
          useValue: {
            addPurchase: jest.fn(),
            processShopifyOrder: jest.fn(),
            generateCodeFromOrderNumber: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<PurchasesService>(PurchasesService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('Shopify Integration', () => {
    it('should handle Shopify order data structure', () => {
      const shopifyOrder: ShopifyOrder = {
        id: '12345',
        order_number: '1001',
        name: '#1001',
        email: '<EMAIL>',
        financial_status: 'paid',
        fulfillment_status: 'unfulfilled',
        total_price: '99.99',
        currency: 'USD',
        customer: {
          first_name: '<PERSON>',
          last_name: 'Doe',
          email: '<EMAIL>',
          phone: '+**********',
        },
        billing_address: {
          address1: '123 Main St',
          address2: 'Apt 4B',
          city: 'New York',
          province: 'NY',
          zip: '10001',
          country: 'USA',
          phone: '+**********',
        },
        line_items: [
          {
            title: 'Training License',
            quantity: 2,
            price: '49.99',
            sku: 'LICENSE-001',
          },
        ],
      };

      expect(shopifyOrder).toBeDefined();
      expect(shopifyOrder.customer?.first_name).toBe('John');
      expect(shopifyOrder.line_items?.length).toBe(1);
    });

    it('should convert Shopify order to CreatePurchaseDto', () => {
      const mockService = {
        convertShopifyOrderToPurchase: jest.fn().mockReturnValue({
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          code: '1001',
          numberOfVrGlasses: 0,
          numberOfLicenses: 2,
          isSubscription: false,
          duration: 365,
          orderNumber: '1001',
        }),
      };

      const shopifyOrder: ShopifyOrder = {
        id: '12345',
        order_number: '1001',
        customer: {
          first_name: 'John',
          last_name: 'Doe',
          email: '<EMAIL>',
        },
      };

      const result = mockService.convertShopifyOrderToPurchase(shopifyOrder);
      expect(result).toBeDefined();
      expect(result.email).toBe('<EMAIL>');
      expect(result.firstName).toBe('John');
      expect(result.lastName).toBe('Doe');
    });
  });
});
