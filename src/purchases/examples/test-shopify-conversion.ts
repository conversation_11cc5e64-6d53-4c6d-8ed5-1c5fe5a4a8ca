/**
 * Simple test to verify Shopify order conversion logic
 * This can be run independently to test the conversion functionality
 */

import { ShopifyOrder } from '../interfaces/shopify.interface';

// Mock the unique code generation logic to test it independently
function generateUniqueCode(): string {
  const characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let code = '';
  for (let i = 0; i < 4; i++) {
    code += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return code;
}

function convertShopifyOrderToPurchase(shopifyOrder: ShopifyOrder) {
  // Extract customer information
  const customer = shopifyOrder.customer;
  const billingAddress = shopifyOrder.billing_address;
  const shippingAddress = shopifyOrder.shipping_address || billingAddress;

  // Generate a unique 4-character code
  const code = generateUniqueCode();

  // Extract product information from line items
  const lineItems = shopifyOrder.line_items || [];
  let numberOfVrGlasses = 0;
  let numberOfLicenses = 0;
  let isSubscription = false;
  let duration = 365; // Default duration in days

  // Process line items to determine product quantities and type
  lineItems.forEach(item => {
    const title = (item.title || '').toLowerCase();
    const sku = (item.sku || '').toLowerCase();
    const quantity = item.quantity || 1;

    // Logic to determine VR glasses and licenses based on product title/SKU
    if (title.includes('vr') || title.includes('glasses') || sku.includes('vr')) {
      numberOfVrGlasses += quantity;
    }
    
    if (title.includes('license') || sku.includes('license')) {
      numberOfLicenses += quantity;
    }

    // Check for subscription products
    if (title.includes('subscription') || sku.includes('subscription')) {
      isSubscription = true;
    }

    // Determine duration based on product type
    if (title.includes('monthly') || sku.includes('monthly')) {
      duration = 30;
    } else if (title.includes('yearly') || sku.includes('yearly')) {
      duration = 365;
    }
  });

  // If no specific quantities found, default to 1 license
  if (numberOfLicenses === 0 && numberOfVrGlasses === 0) {
    numberOfLicenses = 1;
  }

  // Combine address lines
  const addressLine = [billingAddress?.address1, billingAddress?.address2]
    .filter(line => line && line.trim())
    .join(' ');

  return {
    email: customer?.email || shopifyOrder.email || '',
    firstName: customer?.first_name || '',
    lastName: customer?.last_name || '',
    code,
    numberOfVrGlasses,
    numberOfLicenses,
    isSubscription,
    duration,
    orderNumber: shopifyOrder.order_number || (shopifyOrder.id ? String(shopifyOrder.id) : ''),
    additional_info: {
      info: `Shopify Order: ${shopifyOrder.name || shopifyOrder.order_number}`,
      is_hidden: false,
      purchase_type: isSubscription ? 'SUBSCRIPTION' : 'START_PACKAGE',
      purchase_source: 'WEBSHOP',
      address_line: addressLine || null,
      city: billingAddress?.city || null,
      state: billingAddress?.province || null,
      postal_code: billingAddress?.zip || null,
      country: billingAddress?.country || null,
      phone: billingAddress?.phone || customer?.phone || null,
      order_amount: shopifyOrder.total_price || null,
      shipped: shopifyOrder.fulfillment_status === 'fulfilled',
    },
  };
}

// Test the conversion with sample data
const testShopifyOrder: ShopifyOrder = {
  id: 12345,
  order_number: '1001',
  name: '#1001',
  email: '<EMAIL>',
  financial_status: 'paid',
  fulfillment_status: 'unfulfilled',
  total_price: '99.99',
  currency: 'USD',
  customer: {
    first_name: 'John',
    last_name: 'Doe',
    email: '<EMAIL>',
    phone: '+**********',
  },
  billing_address: {
    address1: '123 Main St',
    address2: 'Apt 4B',
    city: 'New York',
    province: 'NY',
    zip: '10001',
    country: 'USA',
    phone: '+**********',
  },
  line_items: [
    {
      title: 'VR Training License',
      quantity: 2,
      price: '49.99',
      sku: 'VR-LICENSE-001',
    },
    {
      title: 'VR Glasses',
      quantity: 1,
      price: '199.99',
      sku: 'VR-GLASSES-001',
    },
  ],
};

console.log('Testing Shopify order conversion...');
console.log('Input Shopify Order:', JSON.stringify(testShopifyOrder, null, 2));

const result = convertShopifyOrderToPurchase(testShopifyOrder);
console.log('\nConverted Purchase DTO:', JSON.stringify(result, null, 2));

// Verify the conversion results
console.log('\n=== Conversion Results ===');
console.log(`Email: ${result.email}`);
console.log(`Name: ${result.firstName} ${result.lastName}`);
console.log(`Code: ${result.code}`);
console.log(`VR Glasses: ${result.numberOfVrGlasses}`);
console.log(`Licenses: ${result.numberOfLicenses}`);
console.log(`Is Subscription: ${result.isSubscription}`);
console.log(`Duration: ${result.duration} days`);
console.log(`Order Number: ${result.orderNumber}`);
console.log(`Address: ${result.additional_info?.address_line}`);
console.log(`City: ${result.additional_info?.city}`);
console.log(`Total: ${result.additional_info?.order_amount}`);

export { convertShopifyOrderToPurchase, testShopifyOrder };
