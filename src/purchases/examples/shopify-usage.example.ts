/**
 * Example usage of Shopify integration
 * This file demonstrates how to use the Shopify webhook handler
 */

import { ShopifyOrder } from '../interfaces/shopify.interface';

// Example Shopify order payload that would come from a webhook
const exampleShopifyOrder: ShopifyOrder = {
  id: '12345',
  order_number: '1001',
  name: '#1001',
  email: '<EMAIL>',
  financial_status: 'paid',
  fulfillment_status: 'unfulfilled',
  total_price: '99.99',
  currency: 'USD',
  created_at: '2023-01-01T00:00:00Z',
  customer: {
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '+**********',
  },
  billing_address: {
    address1: '123 Main St',
    address2: 'Apt 4B',
    city: 'New York',
    province: 'NY',
    zip: '10001',
    country: 'USA',
    phone: '+**********',
  },
  shipping_address: {
    address1: '123 Main St',
    city: 'New York',
    province: 'NY',
    zip: '10001',
    country: 'USA',
  },
  line_items: [
    {
      title: 'VR Training License',
      quantity: 2,
      price: '49.99',
      sku: 'VR-LICENSE-001',
      product_id: '67890',
      variant_id: '11111',
    },
    {
      title: 'VR Glasses',
      quantity: 1,
      price: '199.99',
      sku: 'VR-GLASSES-001',
      product_id: '67891',
      variant_id: '11112',
    },
  ],
};

/**
 * Example of how the service would process this order:
 * 
 * 1. The webhook endpoint receives the Shopify order
 * 2. It calls purchasesService.convertShopifyOrderToPurchase(shopifyOrder)
 * 3. This returns a CreatePurchaseDto with:
 *    - email: '<EMAIL>'
 *    - firstName: 'John'
 *    - lastName: 'Doe'
 *    - code: 'aB3x' (unique 4-char code with letters+numbers)
 *    - numberOfVrGlasses: 1 (detected from line items)
 *    - numberOfLicenses: 2 (detected from line items)
 *    - isSubscription: false (detected from line items)
 *    - duration: 365 (default)
 *    - orderNumber: '1001'
 *    - additional_info: {
 *        info: 'Shopify Order: #1001',
 *        purchase_type: 'START_PACKAGE',
 *        purchase_source: 'WEBSHOP',
 *        address_line: '123 Main St Apt 4B',
 *        city: 'New York',
 *        state: 'NY',
 *        postal_code: '10001',
 *        country: 'USA',
 *        phone: '+**********',
 *        order_amount: '99.99',
 *        shipped: false
 *      }
 * 4. The DTO is passed to purchasesService.addPurchase()
 * 5. The purchase is saved to the database
 */

// Example webhook request to test with curl:
const curlExample = `
curl -X POST http://localhost:3000/purchases/shopify-webhook \\
  -H "Content-Type: application/json" \\
  -d '${JSON.stringify(exampleShopifyOrder, null, 2)}'
`;

console.log('Example curl command to test Shopify webhook:');
console.log(curlExample);

export { exampleShopifyOrder, curlExample };
