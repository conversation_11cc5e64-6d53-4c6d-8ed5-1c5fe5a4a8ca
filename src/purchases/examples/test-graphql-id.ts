/**
 * Test GraphQL ID normalization for Shopify webhooks
 */

import { normalizeShopifyId } from '../interfaces/shopify.interface';

// Test cases for GraphQL ID normalization
const testCases = [
  {
    input: 'gid://shopify/Order/12345',
    expected: '12345',
    description: 'Standard Shopify GraphQL Order ID',
  },
  {
    input: 'gid://shopify/Product/67890',
    expected: '67890',
    description: 'Shopify GraphQL Product ID',
  },
  {
    input: '12345',
    expected: '12345',
    description: 'Regular numeric ID',
  },
  {
    input: 12345,
    expected: '12345',
    description: 'Numeric ID as number',
  },
  {
    input: '',
    expected: '',
    description: 'Empty string',
  },
  {
    input: undefined,
    expected: '',
    description: 'Undefined value',
  },
  {
    input: 'gid://shopify/Order/12345/nested/path',
    expected: 'path',
    description: 'Complex GraphQL ID with nested path',
  },
];

console.log('Testing GraphQL ID normalization...\n');

testCases.forEach((testCase, index) => {
  const result = normalizeShopifyId(testCase.input);
  const passed = result === testCase.expected;
  
  console.log(`Test ${index + 1}: ${testCase.description}`);
  console.log(`  Input: ${JSON.stringify(testCase.input)}`);
  console.log(`  Expected: "${testCase.expected}"`);
  console.log(`  Got: "${result}"`);
  console.log(`  Status: ${passed ? '✅ PASS' : '❌ FAIL'}`);
  console.log('');
});

// Test with sample Shopify webhook data that might cause the JSON error
const problematicShopifyData = {
  id: 'gid://shopify/Order/12345',
  order_id: 'gid://shopify/Order/12345',
  order_number: '1001',
  name: '#1001',
  customer: {
    id: 'gid://shopify/Customer/67890',
    first_name: 'John',
    last_name: 'Doe',
    email: '<EMAIL>',
  },
  line_items: [
    {
      id: 'gid://shopify/LineItem/11111',
      product_id: 'gid://shopify/Product/22222',
      variant_id: 'gid://shopify/ProductVariant/33333',
      title: 'VR Training License',
      quantity: 2,
    },
  ],
};

console.log('Sample problematic Shopify data:');
console.log(JSON.stringify(problematicShopifyData, null, 2));

console.log('\nNormalized IDs:');
console.log(`Order ID: ${normalizeShopifyId(problematicShopifyData.id)}`);
console.log(`Order ID (alt): ${normalizeShopifyId(problematicShopifyData.order_id)}`);
console.log(`Customer ID: ${normalizeShopifyId(problematicShopifyData.customer.id)}`);
console.log(`Product ID: ${normalizeShopifyId(problematicShopifyData.line_items[0].product_id)}`);

export { testCases, problematicShopifyData };
