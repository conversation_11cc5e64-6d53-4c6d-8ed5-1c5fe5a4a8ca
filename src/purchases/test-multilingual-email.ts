/**
 * Test script to verify multilingual email functionality
 */

import { EmailTemplateService } from '../mail/email-template.service';
import { ShopifyOrder } from './interfaces/shopify.interface';

// Mock Shopify orders for different countries
const testOrders: { country: string; order: ShopifyOrder }[] = [
  {
    country: 'Sweden',
    order: {
      id: '12345',
      order_number: 'SE-1001',
      total_price: '999.00',
      currency: 'SEK',
      billing_address: {
        country: 'SE',
        city: 'Stockholm',
      },
      customer: {
        first_name: '<PERSON>',
        last_name: '<PERSON><PERSON>',
        email: '<EMAIL>',
      },
    },
  },
  {
    country: 'Finland',
    order: {
      id: '12346',
      order_number: 'FI-1002',
      total_price: '89.90',
      currency: 'EUR',
      billing_address: {
        country: 'FI',
        city: 'Helsinki',
      },
      customer: {
        first_name: 'Aino',
        last_name: 'Virtanen',
        email: '<EMAIL>',
      },
    },
  },
  {
    country: 'Norway',
    order: {
      id: '12347',
      order_number: 'NO-1003',
      total_price: '1200.00',
      currency: 'NOK',
      billing_address: {
        country: 'NO',
        city: 'Oslo',
      },
      customer: {
        first_name: '<PERSON>',
        last_name: '<PERSON>',
        email: '<EMAIL>',
      },
    },
  },
  {
    country: 'Germany',
    order: {
      id: '12348',
      order_number: 'DE-1004',
      total_price: '79.99',
      currency: 'EUR',
      billing_address: {
        country: 'DE',
        city: 'Berlin',
      },
      customer: {
        first_name: 'Hans',
        last_name: 'Mueller',
        email: '<EMAIL>',
      },
    },
  },
  {
    country: 'Denmark',
    order: {
      id: '12349',
      order_number: 'DK-1005',
      total_price: '599.00',
      currency: 'DKK',
      billing_address: {
        country: 'DK',
        city: 'Copenhagen',
      },
      customer: {
        first_name: 'Mette',
        last_name: 'Nielsen',
        email: '<EMAIL>',
      },
    },
  },
  {
    country: 'United States (fallback to English)',
    order: {
      id: '12350',
      order_number: 'US-1006',
      total_price: '99.99',
      currency: 'USD',
      billing_address: {
        country: 'US',
        city: 'New York',
      },
      customer: {
        first_name: 'John',
        last_name: 'Smith',
        email: '<EMAIL>',
      },
    },
  },
];

async function testMultilingualEmails() {
  console.log('🌍 Testing multilingual email templates...\n');

  const emailTemplateService = new EmailTemplateService();

  for (const { country, order } of testOrders) {
    console.log(`📧 Testing ${country}:`);
    
    try {
      // Detect language
      const language = emailTemplateService.detectLanguageFromOrder(order);
      console.log(`   Detected language: ${language}`);

      // Prepare template variables
      const templateVariables = {
        customerName: `${order.customer?.first_name} ${order.customer?.last_name}`,
        activationCode: 'AB3x',
        orderNumber: order.order_number || order.id?.toString() || '',
        currency: order.currency || 'USD',
        totalPrice: order.total_price || '0.00',
        email: order.customer?.email || '<EMAIL>',
      };

      // Get template
      const template = emailTemplateService.getActivationEmailTemplate(
        language,
        templateVariables,
      );

      console.log(`   Subject: ${template.subject}`);
      console.log(`   Template loaded successfully ✅`);
      
      // Show a snippet of the text template
      const textSnippet = template.text.substring(0, 100).replace(/\n/g, ' ');
      console.log(`   Text preview: ${textSnippet}...`);
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }
    
    console.log('');
  }

  console.log('🎉 Multilingual email test completed!');
}

// Test language detection specifically
function testLanguageDetection() {
  console.log('🔍 Testing language detection logic...\n');

  const emailTemplateService = new EmailTemplateService();
  
  const testCases = [
    { country: 'SE', expected: 'sv' },
    { country: 'Sweden', expected: 'sv' },
    { country: 'FI', expected: 'fi' },
    { country: 'Finland', expected: 'fi' },
    { country: 'NO', expected: 'no' },
    { country: 'Norway', expected: 'no' },
    { country: 'DE', expected: 'de' },
    { country: 'Germany', expected: 'de' },
    { country: 'DK', expected: 'da' },
    { country: 'Denmark', expected: 'da' },
    { country: 'US', expected: 'en' },
    { country: 'Unknown', expected: 'en' },
    { country: '', expected: 'en' },
  ];

  for (const { country, expected } of testCases) {
    const mockOrder: ShopifyOrder = {
      billing_address: { country },
    };
    
    const detected = emailTemplateService.detectLanguageFromOrder(mockOrder);
    const status = detected === expected ? '✅' : '❌';
    
    console.log(`${status} Country: "${country}" → Language: "${detected}" (expected: "${expected}")`);
  }

  console.log('\n🎯 Language detection test completed!');
}

// Uncomment to run tests
// testMultilingualEmails().catch(console.error);
// testLanguageDetection();

export { testMultilingualEmails, testLanguageDetection };
