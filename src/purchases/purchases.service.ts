import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { CreatePurchaseDto } from './dto/create-purchase.dto';
import { UpdatePurchaseDto } from './dto/update-purchase.dto';
import { DatabaseService } from 'src/database/database.service';
import { PurchaseActivationDto } from './dto/purchase-activation.dto';
import { PurchaseAdditionalInfoDto } from './dto/purchase_additional_info.dto';
import { FirebaseService } from 'src/firebase/firebase.service';
import { RedisService } from '../redis/redis.service';
import { Decimal } from '@prisma/client/runtime/library';
import { PurchaseSource, PurchaseType } from './enums/purchase.enum';
import {
  ShopifyOrder,
  normalizeShopifyId,
} from './interfaces/shopify.interface';
import { RetryUtil } from '../common/utils/retry.util';

interface PurchaseStatusResponse {
  purchaseId: number;
  purchaseDate: Date;
  orderStatus: any;
  shippingInfo: any;
  additionalInfo: any;
  activationRecords: any[];
  validUntil: Date | null;
}

@Injectable()
export class PurchasesService {
  private readonly logger = new Logger(PurchasesService.name);
  private readonly serviceApiName = 'purchaseAPI';
  private readonly CACHE_TTL = 3600;
  private readonly CACHE_PREFIX = 'purchases';

  constructor(
    private database: DatabaseService,
    private firebase: FirebaseService,
    private readonly redisService: RedisService,
  ) {}

  private generateCacheKey(page: number, limit: number): string {
    const env = process.env.DEV_ENV || 'prod';
    return `${env}_${this.CACHE_PREFIX}_page${page}_limit${limit}`;
  }

  private async invalidatePurchasesCaches(): Promise<void> {
    const methodName = 'invalidatePurchasesCaches';
    try {
      const pattern = `${this.CACHE_PREFIX}_*`;
      const deletedCount = await this.redisService.deleteByPattern(pattern);

      if (deletedCount > 0) {
        this.logger.log(
          `${methodName}: Successfully invalidated ${deletedCount} cache entries`,
        );
      } else {
        this.logger.debug(
          `${methodName}: No cache entries found to invalidate for pattern: ${pattern}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `${methodName}: Error invalidating cache: ${error.message}`,
        error.stack,
      );
    }
  }

  async upsertUserByUuid(uuid: string) {
    const methodName = 'upsertUserByUuid';
    this.logger.log(
      `${this.serviceApiName} ${methodName}: Upserting user by UUID: ${uuid}`,
    );
    try {
      const user = await this.database.user.upsert({
        where: { uuid },
        create: {
          uuid,
          registered_on: new Date(),
          starred: false,
          type: 'standard',
          deleted: false,
        },
        update: {},
      });
      return user;
    } catch (error) {
      this.logger.error(
        `${this.serviceApiName} ${methodName}: Failed to upsert user by UUID: ${uuid}`,
        error.stack,
      );
      throw new HttpException(
        'Failed to find or create user',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async linkUserToPurchaseActivation(activationId: number, userId: number) {
    const methodName = 'linkUserToPurchaseActivation';
    this.logger.log(
      `${this.serviceApiName} ${methodName}: Linking user ID: ${userId} to purchase activation ID: ${activationId}`,
    );
    try {
      const purchaseActivation = await this.database.activation.update({
        where: { id: activationId },
        data: { user_id: userId },
      });
      return purchaseActivation;
    } catch (error) {
      this.logger.error(
        `${this.serviceApiName} ${methodName}: Failed to link user ID: ${userId} to purchase activation ID: ${activationId}`,
        error.stack,
      );
      throw new HttpException(
        'Failed to link user to purchase activation',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  checkActivationLimit(purchase: any): boolean {
    const methodName = 'checkActivationLimit';
    this.logger.log(
      `${this.serviceApiName} ${methodName}: Checking activation limit for purchase ID: ${purchase.id}`,
    );

    if (!purchase.activations || !Array.isArray(purchase.activations)) {
      this.logger.error(
        `${this.serviceApiName} ${methodName}: Activations data is invalid or not an array for purchase ID: ${purchase.id}`,
      );
      throw new HttpException(
        'Invalid activations data',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
    const enabledActivations = purchase.activations.filter(
      (activation) => activation.user_id != null,
    );

    this.logger.log(
      `${this.serviceApiName} ${methodName}: Enabled activations count: ${enabledActivations.length}`,
    );

    if (enabledActivations.length >= purchase.number_of_licenses) {
      this.logger.error(
        `${this.serviceApiName} ${methodName}: Purchase ID: ${purchase.id} exceeded the number of activations`,
      );
      throw new HttpException(
        'The purchase exceeded the number of activations',
        HttpStatus.FORBIDDEN,
      );
    }
    return true;
  }

  async getPurchaseActivations(
    purchaseId: number,
  ): Promise<PurchaseActivationDto[]> {
    const methodName = 'getPurchaseActivations';
    try {
      // First verify if the purchase exists
      const purchase = await this.database.purchase.findUnique({
        where: { id: purchaseId },
      });

      if (!purchase) {
        this.logger.warn(
          `${this.serviceApiName} ${methodName}: Purchase not found with ID: ${purchaseId}`,
        );
        throw new HttpException('Purchase not found', HttpStatus.NOT_FOUND);
      }

      const activations = await this.database.activation.findMany({
        where: {
          purchase_id: purchaseId,
        },
        include: {
          user: {
            include: {
              additional_info: true,
              training_session_data: true,
            },
          },
        },
        orderBy: {
          activation_date: 'desc',
        },
      });

      this.logger.log(
        `${this.serviceApiName} ${methodName}: Successfully retrieved ${activations.length} activations for purchase ID ${purchaseId}`,
      );

      // Transform the result to match PurchaseActivationDto format
      const formattedResult = activations.map((activation) => ({
        id: activation.id,
        purchase_id: activation.purchase_id,
        activation_date: activation.activation_date,
        updated_at: activation.updated_at,
        user_id: activation.user_id,
        user: activation.user
          ? {
              id: activation.user.id,
              uuid: activation.user.uuid,
              registered_on: activation.user.registered_on,
              valid_until: activation.user.valid_until,
              starred: activation.user.starred,
              type: activation.user.type,
              deleted: activation.user.deleted,
              training_session_data: activation.user.training_session_data,
              additional_info: activation.user.additional_info
                ? {
                    name: activation.user.additional_info.name,
                    email: activation.user.additional_info.email,
                    age: activation.user.additional_info.age,
                    vision_problem:
                      activation.user.additional_info.vision_problem,
                    optional_text:
                      activation.user.additional_info.optional_text,
                    accept_newsletter:
                      activation.user.additional_info.accept_newsletter,
                    birthdate: activation.user.additional_info.birthdate,
                    first_name: activation.user.additional_info.first_name,
                    last_name: activation.user.additional_info.last_name,
                    notification_hour:
                      activation.user.additional_info.notification_hour,
                  }
                : undefined,
            }
          : undefined,
      }));

      return formattedResult;
    } catch (error) {
      this.logger.error(
        `${this.serviceApiName} ${methodName}: Error fetching activations for purchase ID ${purchaseId}: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        'Failed to fetch purchase activations',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
  async findPurchaseByCode(code: string) {
    const methodName = 'findPurchaseByCode';
    try {
      this.logger.log(
        `${this.serviceApiName} ${methodName}: Finding purchase by code: ${code}`,
      );
      const purchase = await this.database.purchase.findUnique({
        where: { code },
        include: { activations: true },
      });

      if (!purchase) {
        this.logger.warn(
          `${this.serviceApiName} ${methodName}: No purchase found with code: ${code}`,
        );
        return null;
      }

      return purchase;
    } catch (error) {
      this.logger.error(
        `${this.serviceApiName} ${methodName}: Error finding purchase by code: ${code}`,
        error.stack,
      );
      throw new HttpException(
        'We could not find any purchase corresponding with this activation code',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async createActivation(purchaseId: number) {
    const methodName = 'createActivation';
    try {
      this.logger.log(
        `${this.serviceApiName} ${methodName}: Creating activation for purchase ID: ${purchaseId}`,
      );
      const activation = await this.database.activation.create({
        data: { purchase_id: purchaseId },
      });

      return activation;
    } catch (error) {
      this.logger.error(
        `${this.serviceApiName} ${methodName}: Error creating activation for purchase ID: ${purchaseId}`,
        error.stack,
      );
      throw new HttpException(
        'Internal server error while creating activation',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async activatePurchase(code: string) {
    const methodName = 'activatePurchase';
    try {
      this.logger.log(
        `${this.serviceApiName} ${methodName}: Activating purchase with code: ${code}`,
      );

      // Find the purchase by the activation code
      const purchase = await this.findPurchaseByCode(code);

      // Check if the activation limit is reached
      this.checkActivationLimit(purchase);

      // Check if the purchase is already linked with a student
      const student = await this.database.student.findFirst({
        where: {
          purchase_id: purchase.id,
          userId: null,
        },
      });

      if (student) {
        this.logger.log(
          `${this.serviceApiName} ${methodName}: Purchase with code: ${code} is already linked to a student.`,
        );
      }

      // Create a new activation regardless of whether the purchase is linked with a student or not
      const activation = await this.createActivation(purchase.id);

      // Embed the student object into the activation object if it exists
      const activationWithStudent = {
        ...activation,
        student: student || null,
      };

      // Return the modified activation object
      return activationWithStudent;
    } catch (error) {
      this.logger.error(
        `${this.serviceApiName} ${methodName}: Error activating purchase with code: ${code}`,
        error.stack,
      );

      if (error.response && error.status) {
        throw new HttpException(error.response, error.status);
      } else {
        throw new HttpException(
          'Unexpected error while activating purchase',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    }
  }

  async reactivateAccountWithPurchase(userUUID: string, code: string) {
    const methodName = 'reactivatePurchase';
    this.logger.log(
      `${this.serviceApiName} ${methodName}: Reactivating purchase for user ${userUUID} with code: ${code}`,
    );
    try {
      // Find the purchase by the activation code
      const purchase = await this.findPurchaseByCode(code);
      if (!purchase) {
        this.logger.warn(
          `${this.serviceApiName} ${methodName}: No purchase found with code: ${code}`,
        );
        throw new HttpException(
          'No purchase found with the given code',
          HttpStatus.NOT_FOUND,
        );
      } else {
        // Check if the activation limit is reached
        this.checkActivationLimit(purchase);

        // Create a new activation
        const activation = await this.createActivation(purchase.id);

        const user = await this.upsertUserByUuid(userUUID);

        // Link the activation with the user
        await this.linkUserToPurchaseActivation(activation.id, user.id);

        // Update the EOL for the user in SQL
        const newEOL = new Date(
          new Date().setDate(new Date().getDate() + purchase.duration), // Create a new date by adding the duration to the current date
        );
        await this.database.user.update({
          where: { uuid: userUUID },
          data: { valid_until: newEOL },
        });

        // Update the EOL for the user in Firestore
        const firestore = this.firebase.getFirestore();
        await firestore.collection('UserData').doc(userUUID).update({
          ValidTill: newEOL,
        });
      }
    } catch (error) {
      this.logger.error(
        `${this.serviceApiName} ${methodName}: Error reactivating purchase for user ${userUUID} with code: ${code}`,
        error.stack,
      );
      throw new HttpException(
        'Unexpected error while reactivating purchase',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Convert Shopify order data to CreatePurchaseDto
   */
  async convertShopifyOrderToPurchase(
    shopifyOrder: ShopifyOrder,
  ): Promise<CreatePurchaseDto> {
    const methodName = 'convertShopifyOrderToPurchase';
    // Normalize Shopify IDs to handle GraphQL format
    const normalizedId = normalizeShopifyId(
      shopifyOrder.id || shopifyOrder.order_id,
    );
    const orderNumber = String(
      shopifyOrder.order_number || shopifyOrder.number || normalizedId || '',
    );

    this.logger.log(
      `${this.serviceApiName} ${methodName}: Converting Shopify order ${
        normalizedId || orderNumber
      }`,
    );

    // Extract customer information
    const customer = shopifyOrder.customer;
    const billingAddress = shopifyOrder.billing_address;
    const shippingAddress = shopifyOrder.shipping_address;

    // Generate a unique 4-character code
    const code = await this.generateUniqueCode();

    // Extract product information from line items
    const lineItems = shopifyOrder.line_items || [];
    let numberOfVrGlasses = 0;
    let numberOfLicenses = 0;
    let isSubscription = false;
    enum Duration {
      START_PACKAGE = 84,
      CONTINUE_TRAINING_42 = 42,
      CONTINUE_TRAINING_84 = 85, // Changed to avoid duplicate value
      CONTINUE_TRAINING_180 = 180,
      CONTINUE_TRAINING_360 = 360,
      SUBSCRIPTION = 30,
    }
    let duration = Duration.START_PACKAGE;
    let purchaseType = PurchaseType.START_PACKAGE;

    // Collect all SKUs first to check for combinations
    const skus = lineItems.map((item) => (item.sku || '').toLowerCase());
    const hasSku1 = skus.some((sku) => sku === '1');

    // Process line items to determine product quantities and type
    lineItems.forEach((item) => {
      const title = (item.title || '').toLowerCase();
      const sku = (item.sku || '').toLowerCase();
      const quantity = item.quantity || 1;

      // Logic to determine glasses and licenses based on product title/SKU
      if (
        title.includes('vr') ||
        title.includes('glasses') ||
        sku.includes('vr')
      ) {
        numberOfVrGlasses += quantity;
      }

      if (title.includes('license')) {
        numberOfLicenses += quantity;
      }

      // Check for subscription products
      if (title.includes('subscription')) {
        isSubscription = true;
      }
    });

    if (!hasSku1) {
      purchaseType = PurchaseType.CONTINUE_TRAINING;
    }

    // Determine duration based on SKU combinations and individual SKUs
    if (hasSku1 || skus.some((sku) => sku.includes('5'))) {
      duration = Duration.START_PACKAGE;
    } else if (skus.some((sku) => sku.includes('4'))) {
      duration = Duration.CONTINUE_TRAINING_42;
    } else if (skus.some((sku) => sku.includes('6'))) {
      duration = Duration.CONTINUE_TRAINING_180;
    } else if (skus.some((sku) => sku.includes('7'))) {
      duration = Duration.CONTINUE_TRAINING_360;
    }

    // If no specific quantities found, default to 1 license
    if (numberOfLicenses === 0 && numberOfVrGlasses === 0) {
      numberOfLicenses = 1;
    }

    // Combine address lines
    const addressLine = [billingAddress?.address1, billingAddress?.address2]
      .filter((line) => line && line.trim())
      .join(' ');

    const metafield = {
      order: {
        id: normalizedId,
        metafields: [
          {
            namespace: 'custom',
            key: 'activation_code',
            type: 'single_line_text_field',
            value: code,
          },
        ],
      },
    };

    try {
      await RetryUtil.fetchWithRetry(
        `${process.env.SHOPIFY_STORE}/admin/api/2025-07/orders/${normalizedId}.json`,
        {
          method: 'PUT',
          headers: {
            'X-Shopify-Access-Token': process.env.SHOPIFY_ADMIN_TOKEN,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(metafield),
        },
        {
          maxRetries: 3,
          baseDelay: 1000,
          onRetry: (error, attempt) => {
            this.logger.warn(
              `${methodName}: Shopify API retry attempt ${attempt} for order ${normalizedId}: ${error.message}`,
            );
          },
        },
      );

      // Log successful metafield update
      this.logger.log(
        `${methodName}: Successfully updated Shopify metafield for order ${normalizedId}`,
      );
    } catch (error) {
      this.logger.error(
        `${methodName}: Failed to update Shopify metafield for order ${normalizedId}:`,
        error.stack,
      );
      throw error;
    }

    // Create additional info with Shopify data
    const additionalInfo: PurchaseAdditionalInfoDto = {
      info: `Shopify Order: ${shopifyOrder.name || shopifyOrder.order_number}`,
      is_hidden: false,
      purchase_type: isSubscription ? PurchaseType.SUBSCRIPTION : purchaseType,
      purchase_source: PurchaseSource.WEBSHOP,
      address_line: addressLine || null,
      city: billingAddress?.city || null,
      state: billingAddress?.province || null,
      postal_code: billingAddress?.zip || null,
      country: billingAddress?.country || null,
      phone: billingAddress?.phone || shippingAddress?.phone || null,
      order_amount: shopifyOrder.total_price || null,
      shipped: shopifyOrder.fulfillment_status === 'fulfilled',
    };

    return {
      email: shopifyOrder.email || '',
      firstName: customer?.first_name || '',
      lastName: customer?.last_name || '',
      code,
      numberOfVrGlasses,
      numberOfLicenses,
      isSubscription,
      duration,
      orderNumber,
      additional_info: additionalInfo,
    };
  }

  /**
   * Generate a unique 4-character code with lowercase + UPPERCASE + numbers
   */
  private async generateUniqueCode(): Promise<string> {
    const characters =
      'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let code: string;
    let isUnique = false;
    let attempts = 0;
    const maxAttempts = 100; // Prevent infinite loop

    do {
      code = '';
      for (let i = 0; i < 4; i++) {
        code += characters.charAt(
          Math.floor(Math.random() * characters.length),
        );
      }

      // Check if code is unique in database
      const existingPurchase = await this.database.purchase.findUnique({
        where: { code },
      });

      isUnique = !existingPurchase;
      attempts++;

      if (attempts >= maxAttempts) {
        const error = new Error(
          `Failed to generate unique code after ${maxAttempts} attempts`,
        );
        this.logger.error(`${this.serviceApiName}: ${error.message}`);
        throw new HttpException(
          'Failed to generate unique activation code',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    } while (!isUnique);

    this.logger.log(
      `${this.serviceApiName} generateUniqueCode: Generated unique code: ${code} (attempts: ${attempts})`,
    );

    return code;
  }

  async addPurchase(data: CreatePurchaseDto) {
    const methodName = 'addPurchase';
    this.logger.log(
      `${this.serviceApiName} ${methodName}: Adding new purchase with code: ${data.code}`,
    );

    try {
      const purchaseData = {
        email: data.email,
        first_name: data.firstName,
        last_name: data.lastName,
        code: data.code,
        number_of_vr_glasses: data.numberOfVrGlasses,
        number_of_licenses: data.numberOfLicenses,
        is_subscription: data.isSubscription,
        duration: data.duration,
        order_number: data.orderNumber,
      };

      // Handle additional info with address and amount
      if (data.additional_info) {
        Object.assign(purchaseData, {
          additional_info: {
            create: {
              info: data.additional_info.info || '',
              is_hidden: false,
              purchase_type: data.additional_info.purchase_type || 'DEFAULT',
              purchase_source:
                data.additional_info.purchase_source || 'DEFAULT',
              // Add new address and amount fields
              address_line: data.additional_info.address_line || null,
              city: data.additional_info.city || null,
              state: data.additional_info.state || null,
              postal_code: data.additional_info.postal_code || null,
              country: data.additional_info.country || null,
              phone: data.additional_info.phone || null,
              order_amount: data.additional_info.order_amount
                ? new Decimal(data.additional_info.order_amount)
                : null,
              shipped: data.additional_info.shipped || false,
            },
          },
        });
      }

      const purchase = await this.database.purchase.create({
        data: purchaseData,
        include: {
          additional_info: true,
        },
      });

      // Invalidate cache after adding new purchase
      await this.invalidatePurchasesCaches();

      return purchase;
    } catch (error) {
      this.logger.error(
        `${this.serviceApiName} ${methodName}: Failed to add purchase with code: ${data.code}: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Failed to add purchase',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  findAll() {
    this.logger.log(`${this.serviceApiName}:Fetching all purchases`);
    return this.database.purchase.findMany();
  }

  findOne(id: number) {
    this.logger.log(`${this.serviceApiName}:Fetching purchase by ID: ${id}`);
    return `This action returns a #${id} purchase`;
  }

  async getPurchaseInfo(id: string) {
    const methodName = 'getPurchaseInfo';
    this.logger.log(
      `${this.serviceApiName} ${methodName}:Getting purchase info for ID: ${id}`,
    );
    const numericId = parseInt(id, 10);
    const purchase = await this.database.purchase.findUnique({
      where: { id: numericId },
    });

    if (purchase) {
      return purchase;
    } else {
      this.logger.warn(`No purchase found with ID: ${id}`);
      return `No purchase found with id #${id}`;
    }
  }

  update(id: number) {
    this.logger.log(`${this.serviceApiName}:Updating purchase with ID: ${id}`);
    return `This action updates a #${id} purchase`;
  }

  remove(id: number) {
    this.logger.log(`${this.serviceApiName}:Removing purchase with ID: ${id}`);
    return `This action removes a #${id} purchase`;
  }

  async getPurchases(page: number = 1, limit: number = 100) {
    const methodName = 'getPurchases';
    this.logger.log(
      `${this.serviceApiName} ${methodName}: Getting all purchases`,
    );

    try {
      const validPage = Math.max(page, 1);
      const offset = (validPage - 1) * limit;
      const cacheKey = this.generateCacheKey(validPage, limit);

      // Try to get from cache first
      const cachedData = await this.redisService.get(cacheKey);
      if (cachedData) {
        this.logger.log(`${methodName}: Returning cached data for ${cacheKey}`);
        return JSON.parse(cachedData);
      }

      // If not in cache, fetch from database
      const purchases = await this.database.purchase.findMany({
        skip: offset,
        take: limit,
        include: {
          activations: {
            include: {
              user: {
                include: {
                  training_session_data: true,
                },
              },
            },
          },
          additional_info: true,
        },
      });

      if (!purchases) {
        this.logger.warn(`${methodName}: Purchases not found`);
        return null;
      }

      // Cache the results
      await this.redisService.set(
        cacheKey,
        JSON.stringify(purchases),
        this.CACHE_TTL,
      );
      return purchases;
    } catch (error) {
      this.logger.error(
        `${methodName}: Error fetching purchases: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Failed to fetch purchases',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getPurchasesCount(): Promise<number> {
    const apiName = '[getPurchasesCount API]';
    try {
      this.logger.log(`${apiName} Fetching total purchase count.`);

      // Count purchases
      const totalCount = await this.database.purchase.count();

      return totalCount;
    } catch (error) {
      this.logger.error(
        `${apiName} Error retrieving purchases count: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async findPurchaseByOrderNumber(order_number: string) {
    const methodName = 'findPurchaseByOrderNumber';
    try {
      const purchase = await this.database.purchase.findFirst({
        where: { order_number },
      });

      if (!purchase) {
        this.logger.warn(
          `${this.serviceApiName} ${methodName}: No purchase found with order number: ${order_number}`,
        );
      }
      return purchase;
    } catch (error) {
      this.logger.error(
        `${this.serviceApiName} ${methodName}: Error finding purchase by order number: ${order_number}`,
        error.stack,
      );
      return null;
    }
  }

  async getOrderStatus(orderId: string) {
    const methodName = 'getOrderStatus';
    this.logger.log(
      `${this.serviceApiName} ${methodName}:Getting order status for order id: ${orderId}`,
    );
    try {
      const orderStatus = await this.database.orderStatus.findFirst({
        where: { order_id: orderId },
      });
      if (!orderStatus) {
        this.logger.warn(
          `${this.serviceApiName} ${methodName}: No Order status found for order id: ${orderId}`,
        );
      }
      return orderStatus;
    } catch (error) {
      this.logger.error(
        `${this.serviceApiName} ${methodName}: Error finding order status for order id: ${orderId}`,
        error.stack,
      );
      return null;
    }
  }

  async getOrderShippingInfo(id: string) {
    const methodName = 'getOrderShippingInfo';
    this.logger.log(
      `${this.serviceApiName} ${methodName}:Getting shipping info for purchase ID: ${id}`,
    );
    try {
      const numericId = parseInt(id, 10);
      const shippingInfo = await this.database.shipping_info.findUnique({
        where: { purchase_id: numericId },
      });
      if (!shippingInfo) {
        this.logger.warn(
          `${this.serviceApiName} ${methodName}: No shipping info found for purchase id: ${id}`,
        );
      }

      return shippingInfo;
    } catch (error) {
      this.logger.error(
        `${this.serviceApiName} ${methodName}: Error finding shipping info found for purchase id: ${id}`,
        error.stack,
      );
      return null;
    }
  }

  async updatePurchase(id: number, updatePurchaseDto: UpdatePurchaseDto) {
    const methodName = 'updatePurchase';
    try {
      // First check if purchase exists
      const existingPurchase = await this.database.purchase.findUnique({
        where: { id },
      });

      if (!existingPurchase) {
        this.logger.warn(
          `${this.serviceApiName} ${methodName}: Purchase not found with ID: ${id}`,
        );
        throw new HttpException('Purchase not found', HttpStatus.NOT_FOUND);
      }

      // If code is being updated, check if new code already exists
      if (updatePurchaseDto.code) {
        const purchaseWithCode = await this.database.purchase.findFirst({
          where: {
            code: updatePurchaseDto.code,
            NOT: {
              id: id,
            },
          },
        });

        if (purchaseWithCode) {
          this.logger.warn(
            `${this.serviceApiName} ${methodName}: Code ${updatePurchaseDto.code} already exists`,
          );
          throw new HttpException(
            'Purchase code already exists',
            HttpStatus.CONFLICT,
          );
        }
      }

      // Update the purchase
      const updatedPurchase = await this.database.purchase.update({
        where: { id },
        data: {
          email: updatePurchaseDto.email,
          code: updatePurchaseDto.code,
          duration: updatePurchaseDto.duration,
          number_of_licenses: updatePurchaseDto.number_of_licenses,
          number_of_vr_glasses: updatePurchaseDto.number_of_vr_glasses,
          order_number: updatePurchaseDto.order_number,
        },
        include: {
          activations: true,
          students: true,
          shipping_Info: true,
          orderStatus: true,
        },
      });

      // Invalidate cache after updating purchase
      await this.invalidatePurchasesCaches();

      this.logger.log(
        `${this.serviceApiName} ${methodName}: Successfully updated purchase with ID: ${id}`,
      );

      return updatedPurchase;
    } catch (error) {
      this.logger.error(
        `${this.serviceApiName} ${methodName}: Error updating purchase with ID ${id}: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        'Failed to update purchase',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async createPurchaseAdditionalInfo(
    purchaseId: number,
    createDto: PurchaseAdditionalInfoDto,
  ) {
    const methodName = 'createPurchaseAdditionalInfo';
    try {
      return await this.database.purchase_additional_info.create({
        data: {
          purchase_id: purchaseId,
          info: createDto.info,
          purchase_type: createDto.purchase_type,
          purchase_source: createDto.purchase_source,
          address_line: createDto.address_line,
          city: createDto.city,
          state: createDto.state,
          postal_code: createDto.postal_code,
          country: createDto.country,
          phone: createDto.phone,
          order_amount: createDto.order_amount
            ? Number(createDto.order_amount)
            : null,
          shipped: createDto.shipped || false,
        },
      });
    } catch (error) {
      this.logger.error(
        `${this.serviceApiName} ${methodName}: Error creating purchase additional info: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Failed to create purchase additional info',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updatePurchaseAdditionalInfo(
    id: number,
    updateDto: PurchaseAdditionalInfoDto,
  ) {
    const methodName = 'updatePurchaseAdditionalInfo';
    try {
      return await this.database.purchase_additional_info.update({
        where: {
          id: id,
        },
        data: {
          info: updateDto.info,
          is_hidden: updateDto.is_hidden,
          purchase_type: updateDto.purchase_type,
          purchase_source: updateDto.purchase_source,
          address_line: updateDto.address_line,
          city: updateDto.city,
          state: updateDto.state,
          postal_code: updateDto.postal_code,
          country: updateDto.country,
          phone: updateDto.phone,
          order_amount: updateDto.order_amount
            ? Number(updateDto.order_amount)
            : undefined,
          shipped: updateDto.shipped,
        },
      });
    } catch (error) {
      this.logger.error(
        `${this.serviceApiName} ${methodName}: Error updating purchase additional info: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Failed to update purchase additional info',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async findPurchaseAdditionalInfoById(id: number) {
    return this.database.purchase_additional_info.findFirst({
      where: {
        id: id,
      },
    });
  }

  async findPurchaseAdditionalInfo(purchaseId: number) {
    return this.database.purchase_additional_info.findMany({
      where: {
        purchase_id: purchaseId,
      },
    });
  }

  async getPurchaseStatusesByDateRange(
    startDate: Date,
    endDate: Date,
  ): Promise<Record<number, PurchaseStatusResponse>> {
    const methodName = 'getPurchaseStatusesByDateRange';
    try {
      const cacheKey = `${
        process.env.DEV_ENV || 'prod'
      }_purchase_statuses_${startDate.toISOString()}_${endDate.toISOString()}`;

      // Try to get from cache first
      const cachedData = await this.redisService.get(cacheKey);
      if (cachedData) {
        this.logger.log(`${methodName}: Returning cached data for ${cacheKey}`);
        return JSON.parse(cachedData);
      }

      this.logger.log(
        `${methodName}: Fetching purchases for date range: ${startDate} to ${endDate}`,
      );

      // If not in cache, fetch from database
      const purchases = await this.database.purchase.findMany({
        where: {
          created_at: {
            gte: startDate,
            lte: endDate,
          },
        },
        include: {
          activations: {
            include: {
              user: {
                include: {
                  training_session_data: true,
                },
              },
            },
          },
          orderStatus: true,
          shipping_Info: true,
          additional_info: true,
        },
      });

      if (!purchases) {
        throw new HttpException(
          'No purchases found for the specified date range',
          HttpStatus.NOT_FOUND,
        );
      }

      const statusesMap: Record<number, PurchaseStatusResponse> = {};

      for (const purchase of purchases) {
        const orderStatus = purchase.orderStatus;
        const shippingInfo = purchase.shipping_Info;
        const activationRecords = purchase.activations;
        const validUntil = activationRecords[0]?.user?.valid_until;
        const additionalInfo = purchase.additional_info;

        statusesMap[purchase.id] = {
          purchaseId: purchase.id,
          purchaseDate: purchase.created_at,
          orderStatus,
          shippingInfo,
          additionalInfo,
          activationRecords,
          validUntil,
        };
      }

      // Cache the results for 1 hour
      await this.redisService.set(
        cacheKey,
        JSON.stringify(statusesMap),
        this.CACHE_TTL,
      );

      this.logger.log(
        `${methodName}: Successfully processed ${purchases.length} purchases`,
      );
      return statusesMap;
    } catch (error) {
      this.logger.error(
        `${methodName}: Error processing purchase statuses: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        'Failed to fetch purchase info',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
