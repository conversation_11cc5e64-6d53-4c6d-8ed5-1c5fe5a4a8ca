# Shopify Integration for Purchase Management

This document describes how the purchase system handles Shopify orders and webhooks.

## Overview

The system supports processing Shopify orders through a dedicated webhook endpoint:
- **Webhook Processing**: Using the `/shopify-webhook` endpoint for automatic processing of Shopify orders

## Features

- Automatic extraction of customer information from Shopify orders
- Product analysis to determine VR glasses and license quantities
- Address and billing information processing
- Order status tracking
- Duplicate order prevention
- Comprehensive logging and activity tracking

## Endpoints

### Shopify Webhook Handler

**Endpoint**: `POST /purchases/shopify-webhook`

**Description**: Processes incoming Shopify order webhooks automatically. This endpoint is designed to be called by Shopify when orders are created or updated.

**Example Shopify Webhook Payload**:
```json
{
  "id": 12345,
  "order_number": "1001",
  "name": "#1001",
  "email": "<EMAIL>",
  "financial_status": "paid",
  "fulfillment_status": "unfulfilled",
  "total_price": "99.99",
  "currency": "USD",
  "created_at": "2023-01-01T00:00:00Z",
  "customer": {
    "first_name": "<PERSON>",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "phone": "+**********"
  },
  "billing_address": {
    "address1": "123 Main St",
    "address2": "Apt 4B",
    "city": "New York",
    "province": "NY",
    "zip": "10001",
    "country": "USA",
    "phone": "+**********"
  },
  "shipping_address": {
    "address1": "123 Main St",
    "city": "New York",
    "province": "NY",
    "zip": "10001",
    "country": "USA"
  },
  "line_items": [
    {
      "title": "VR Training License",
      "quantity": 2,
      "price": "49.99",
      "sku": "VR-LICENSE-001",
      "product_id": "67890",
      "variant_id": "11111"
    }
  ]
}
```

## Product Detection Logic

The system analyzes line items to determine product types:

- **VR Glasses**: Items with titles/SKUs containing "vr", "glasses"
- **Licenses**: Items with titles/SKUs containing "license"
- **Subscriptions**: Items with titles/SKUs containing "subscription"
- **Duration**: 
  - Monthly products: 30 days
  - Yearly products: 365 days
  - Default: 365 days

## Code Generation

Purchase codes are generated as unique 4-character codes:
- Uses lowercase letters (a-z), uppercase letters (A-Z), and numbers (0-9)
- Ensures uniqueness by checking against existing purchase codes in database
- Automatically retries if duplicate is found (max 100 attempts)
- Examples: `aB3x`, `K9mP`, `7Qw2`

## Error Handling

- **Duplicate Orders**: Returns 409 Conflict if order already processed
- **Invalid Data**: Returns 400 Bad Request for malformed data
- **Processing Errors**: Returns 500 Internal Server Error with detailed logging

## Activity Logging

All Shopify order processing is logged with:
- Shopify order ID and number
- Financial and fulfillment status
- Order amount and currency
- Processing timestamps
- Error details (if any)

## Setup Instructions

1. **Configure Shopify Webhook**:
   - In Shopify Admin, go to Settings > Notifications
   - Add webhook URL: `https://your-domain.com/purchases/shopify-webhook`
   - Select "Order creation" and "Order updates" events
   - Set format to JSON

2. **Authentication**: The webhook endpoint is excluded from authentication middleware

3. **Testing**: Use the provided test cases or Shopify's webhook testing tools

## Security Considerations

- Webhook endpoint is publicly accessible (required for Shopify)
- Consider implementing webhook signature verification
- Monitor for suspicious activity through admin activity logs
- Implement rate limiting if needed

## Monitoring

- Check admin activity logs for webhook processing
- Monitor purchase creation patterns
- Set up alerts for failed webhook processing
