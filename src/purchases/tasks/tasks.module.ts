import { Modu<PERSON> } from '@nestjs/common';
import { TasksService } from './tasks.service';
import { DatabaseModule } from 'src/database/database.module';
import { PurchasesService } from '../purchases.service';
import { RedisModule } from '../../redis/redis.module';
import { TasksController } from './tasks.controller';
import { PurchaseNotificationModule } from '../notifications/notification.module';
import { TodosModule } from '../../todos/todos.module';
import { TodosService } from '../../todos/todos.service';
import { MailModule } from '../../mail/mail.module';

@Module({
  controllers: [TasksController],
  providers: [TasksService, PurchasesService, TodosService],
  imports: [
    DatabaseModule,
    RedisModule,
    PurchaseNotificationModule,
    TodosModule,
    MailModule,
  ],
})
export class TasksModule {}
