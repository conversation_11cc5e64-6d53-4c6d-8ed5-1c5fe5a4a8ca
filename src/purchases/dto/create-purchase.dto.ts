import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsN<PERSON>ber,
  IsEmail,
  IsNotEmpty,
  Length,
  IsBoolean,
  IsOptional,
  ValidateNested,
  IsObject,
} from 'class-validator';
import { PurchaseAdditionalInfoDto } from './purchase_additional_info.dto';
import { Type } from 'class-transformer';

export class CreatePurchaseDto {
  @ApiProperty({
    required: true,
    description:
      'Email address of the purchaser. Must be a valid email format.',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({ required: true, description: 'First name of the purchaser.' })
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({ required: true, description: 'Last name of the purchaser.' })
  @IsString()
  @IsNotEmpty()
  lastName: string;

  @ApiProperty({
    required: true,
    description:
      'Unique code associated with the purchase. Exactly 4 characters long.',
  })
  @IsString()
  @IsNotEmpty()
  @Length(4, 4)
  code: string;

  @ApiProperty({
    required: true,
    description: 'Number of glasses included in the purchase.',
  })
  @IsNumber()
  @IsNotEmpty()
  numberOfVrGlasses: number;

  @ApiProperty({
    required: true,
    description: 'Number of licenses included in the purchase.',
  })
  @IsNumber()
  @IsNotEmpty()
  numberOfLicenses: number;

  @ApiProperty({
    required: true,
    description: 'Indicates whether the purchase is a subscription.',
  })
  @IsBoolean()
  isSubscription: boolean;

  @ApiProperty({
    required: true,
    description: 'Duration of the purchase , applicable for subscriptions.',
  })
  @IsNumber()
  duration: number;

  @ApiProperty({
    required: true,
    description: 'Order number associated with the purchase.',
  })
  @IsString()
  orderNumber: string;

  @ApiPropertyOptional({
    description: 'Additional information about the purchase',
    type: PurchaseAdditionalInfoDto,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => PurchaseAdditionalInfoDto)
  additional_info?: PurchaseAdditionalInfoDto;
}
