import {
  IsBoolean,
  IsEnum,
  IsOptional,
  IsString,
  IsDecimal,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { PurchaseType, PurchaseSource } from '../enums/purchase.enum';

export class PurchaseAdditionalInfoDto {
  @ApiProperty({
    description: 'Additional information about the purchase',
    example: 'Customer requested express shipping',
  })
  @IsString()
  @IsOptional()
  info: string;

  @ApiProperty({
    required: false,
    description: 'Indicates whether the purchase is marked as hidden',
  })
  @IsBoolean()
  @IsOptional()
  is_hidden: boolean;

  @ApiProperty({
    required: false,
    description: 'Indicates the purchase type',
    enum: PurchaseType,
  })
  @IsEnum(PurchaseType)
  @IsOptional()
  purchase_type?: PurchaseType;

  @ApiProperty({
    required: false,
    description: 'Indicates the purchase source',
    enum: PurchaseSource,
  })
  @IsEnum(PurchaseSource)
  @IsOptional()
  purchase_source?: PurchaseSource;

  @ApiProperty({
    required: false,
    description: 'Customer address line',
    example: '123 Main Street',
  })
  @IsString()
  @IsOptional()
  address_line?: string;

  @ApiProperty({
    required: false,
    description: 'Customer city',
    example: 'New York',
  })
  @IsString()
  @IsOptional()
  city?: string;

  @ApiProperty({
    required: false,
    description: 'Customer state',
    example: 'NY',
  })
  @IsString()
  @IsOptional()
  state?: string;

  @ApiProperty({
    required: false,
    description: 'Customer postal code',
    example: '10001',
  })
  @IsString()
  @IsOptional()
  postal_code?: string;

  @ApiProperty({
    required: false,
    description: 'Customer country',
    example: 'USA',
  })
  @IsString()
  @IsOptional()
  country?: string;

  @ApiProperty({
    required: false,
    description: 'Customer phone number',
    example: '+1234567890',
  })
  @IsString()
  @IsOptional()
  phone?: string;

  @ApiProperty({
    required: false,
    description: 'Order amount',
    example: '99.99',
  })
  @IsDecimal()
  @IsOptional()
  order_amount?: string;

  @ApiProperty({
    required: false,
    description: 'Indicates whether the purchase has been shipped',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  shipped?: boolean;
}
