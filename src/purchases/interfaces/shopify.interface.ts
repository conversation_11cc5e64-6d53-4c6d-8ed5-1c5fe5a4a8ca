export interface ShopifyCustomer {
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
}

export interface ShopifyAddress {
  address1?: string;
  address2?: string;
  city?: string;
  province?: string;
  zip?: string;
  country?: string;
  phone?: string;
}

export interface ShopifyLineItem {
  title?: string;
  quantity?: number;
  price?: string;
  sku?: string;
  product_id?: string;
  variant_id?: string;
  product_type?: string;
}

export interface ShopifyOrder {
  id?: string | number;
  order_number?: string;
  name?: string;
  email?: string;
  financial_status?: string;
  fulfillment_status?: string;
  total_price?: string;
  currency?: string;
  created_at?: string;
  customer?: ShopifyCustomer;
  billing_address?: ShopifyAddress;
  shipping_address?: ShopifyAddress;
  line_items?: ShopifyLineItem[];
  order_id?: string;
  number?: string | number;
  quantity?: number;
}

// Helper function to normalize Shopify GraphQL IDs
export function normalizeShopifyId(id: string | number | undefined): string {
  if (!id) return '';

  const idStr = String(id);

  // Handle GraphQL IDs like "gid://shopify/Order/12345"
  if (idStr.startsWith('gid://')) {
    const parts = idStr.split('/');
    return parts[parts.length - 1] || idStr;
  }

  return idStr;
}
