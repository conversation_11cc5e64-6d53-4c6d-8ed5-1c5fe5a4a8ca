// src/training-list/dto/create-training-item.dto.ts
import { IsEnum, IsInt, IsOptional, IsString, IsUrl } from 'class-validator';
enum TrainingItemType {
  IMAGE = 'image',
  VIDEO = 'video',
  BINOGI = 'binogi',
  ONLINE = 'online',
}
export class CreateTrainingItemDto {
  /** Must be one of the enum values <PERSON>risma generated */
  @IsEnum(TrainingItemType)
  type: TrainingItemType;

  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsUrl()
  url?: string;
  @IsInt() @IsOptional() durationSeconds?: number;
}
