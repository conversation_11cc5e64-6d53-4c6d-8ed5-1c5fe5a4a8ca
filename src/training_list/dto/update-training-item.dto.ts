// src/training-list/dto/update-training-item.dto.ts
import {
  IsEnum,
  IsInt,
  IsOptional,
  IsString,
  IsUrl,
  Min,
} from 'class-validator';
enum TrainingItemType {
  IMAGE = 'image',
  VIDEO = 'video',
  BINOGI = 'binogi',
  ONLINE = 'online',
}
export class UpdateTrainingItemDto {
  @IsOptional()
  @IsEnum(TrainingItemType)
  type?: TrainingItemType;

  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsUrl()
  url?: string;

  @IsOptional()
  @IsInt()
  @Min(0)
  orderNo?: number;
}
