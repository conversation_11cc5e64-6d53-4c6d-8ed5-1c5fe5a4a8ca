// src/training-list/dto/create-training-list.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsUUID, IsInt, Min, IsBoolean } from 'class-validator';

export class CreateTrainingListDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'UUID of the teacher owning this list',
  })
  @IsString()
  teacherUuid: string;

  @ApiProperty({
    example: 17,
    description: 'ID of the school class this list belongs to',
  })
  @IsInt()
  @Min(1)
  schoolClassId: number;

  @ApiProperty({
    description: 'The name/title of the training list',
  })
  @IsString()
  name: string;

  @ApiProperty({
    example: true,
    description: 'Whether the list is public (true) or private (false)',
  })
  @IsBoolean()
  isPublic: boolean;
  @ApiProperty({ description: 'Human-readable class name' })
  className: string;

  @ApiProperty({ description: 'Number of items in this list' })
  itemCount: number;

  @ApiProperty({ description: 'Sum of all item durations (in seconds)' })
  totalDurationSeconds: number;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}
