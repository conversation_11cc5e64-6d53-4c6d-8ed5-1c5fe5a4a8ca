import { IsString, IsBoolean, IsOptional, IsInt } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class UpdateTrainingListDto {
  @ApiPropertyOptional({ description: 'New name for the training list' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: 'Public visibility flag' })
  @IsOptional()
  @IsBoolean()
  isPublic?: boolean;

  @ApiPropertyOptional({ description: 'Associated school class ID' })
  @IsOptional()
  @IsInt()
  schoolClassId?: number;
}
