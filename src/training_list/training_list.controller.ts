import {
  Controller,
  Get,
  Param,
  Post,
  Body,
  HttpStatus,
  Res,
  Query,
  HttpException,
  Delete,
  Put,
  HttpCode,
  ValidationPipe,
  UsePipes,
  NotFoundException,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiOperation,
  ApiTags,
  ApiResponse,
  ApiBadRequestResponse,
  ApiQuery,
} from '@nestjs/swagger';
import { Response } from 'express';
import { TrainingListService } from './training_list.service';
import { CreateTrainingListDto } from './dto/create-training-list.dto';
import { VideoResponseDto } from './dto/video-response.dto';
import { CreateTrainingItemDto } from './dto/create-training-item.dto';
import { UpdateTrainingItemDto } from './dto/update-training-item.dto';
import { VideoDto } from './dto/video.dto';
import { TrainingListWithMetaDto } from './dto/training-list-with-meta.dto';
import { UpdateTrainingListDto } from './dto/update-training-list.dto';

@ApiTags('training-list')
@Controller('training-list')
export class TrainingListController {
  constructor(private readonly trainingListService: TrainingListService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new training list' })
  @ApiResponse({
    status: 201,
    description: 'The training list has been created.',
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
  async create(@Body() dto: CreateTrainingListDto): Promise<any> {
    return this.trainingListService.create(dto);
  }
  @Put(':listId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Rename or update a training list' })
  @UsePipes(new ValidationPipe({ whitelist: true }))
  update(
    @Param('listId', ParseIntPipe) listId: number,
    @Body() dto: UpdateTrainingListDto,
  ) {
    return this.trainingListService.updateList(listId, dto);
  }

  @Delete(':listId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a training list (cascade items)' })
  delete(@Param('listId', ParseIntPipe) listId: number) {
    return this.trainingListService.deleteList(listId);
  }
  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Get all training lists for a teacher by UUID' })
  @ApiQuery({
    name: 'teacherUuid',
    required: true,
    description: 'UUID of the teacher',
  })
  @ApiResponse({
    status: 200,
    description: 'List of training lists with class name & total duration',
    type: [TrainingListWithMetaDto],
  })
  @ApiBadRequestResponse({ description: 'Missing or invalid teacherUuid' })
  @UsePipes(new ValidationPipe({ transform: true }))
  async findAllByTeacher(
    @Query('teacherUuid') teacherUuid: string,
  ): Promise<TrainingListWithMetaDto[]> {
    if (!teacherUuid) {
      throw new NotFoundException('teacherUuid query parameter is required');
    }
    return this.trainingListService.findAllByTeacherUuid(teacherUuid);
  }
  @Put(':listId/activate')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Set one list active (others inactive)' })
  @ApiResponse({ status: 200, description: 'Activated.' })
  async activate(@Param('listId', ParseIntPipe) listId: number): Promise<void> {
    return this.trainingListService.activateList(listId);
  }

  @Get('classes')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: "Get all school classes a teacher's students belong to",
  })
  @ApiQuery({
    name: 'teacherUuid',
    required: true,
    description: 'UUID of the teacher',
  })
  @ApiResponse({
    status: 200,
    description: 'Array of classes',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'integer' },
          name: { type: 'string' },
        },
      },
    },
  })
  @ApiBadRequestResponse({ description: 'Missing or invalid teacherUuid' })
  async getTeacherClasses(
    @Query('teacherUuid') teacherUuid: string,
  ): Promise<Array<{ id: number; name: string }>> {
    if (!teacherUuid) {
      throw new NotFoundException('teacherUuid query parameter is required');
    }
    return this.trainingListService.findClassesByTeacherUuid(teacherUuid);
  }
  @Get('video/:videoId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Fetch metadata for a YouTube video' })
  @ApiResponse({ status: 200, type: VideoResponseDto })
  async getVideo(@Param('videoId') videoId: string): Promise<VideoResponseDto> {
    return this.trainingListService.fetchYouTubeVideo(videoId);
  }
  @Post(':listId/items')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Add a content item to a training list' })
  @ApiResponse({ status: 201, description: 'The item was added.' })
  async createItem(
    @Param('listId', ParseIntPipe) listId: number,
    @Body() dto: CreateTrainingItemDto,
  ) {
    return this.trainingListService.createItem(listId, dto);
  }

  @Get(':listId/items')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Get all items for a training list' })
  async findItemsByList(@Param('listId', ParseIntPipe) listId: number) {
    return this.trainingListService.findItemsByList(listId);
  }

  @Delete('items/:itemId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a training item' })
  async deleteItem(@Param('itemId', ParseIntPipe) itemId: number) {
    await this.trainingListService.deleteItem(itemId);
  }

  @Put('items/:itemId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Reorder (update orderNo) of a training item' })
  async updateItem(
    @Param('itemId', ParseIntPipe) itemId: number,
    @Body('orderNo') orderNo: number,
  ) {
    return this.trainingListService.updateItemOrder(itemId, orderNo);
  }
  @Get('svt/:svtId')
  async getSvtVideo(@Param('svtId') svtId: string): Promise<VideoDto> {
    try {
      return await this.trainingListService.fetchSvtVideo(svtId);
    } catch (err) {
      throw new HttpException(
        'Could not fetch SVT Play data',
        HttpStatus.BAD_GATEWAY,
      );
    }
  }
  @Get('ur/:urId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Fetch metadata for a UR Play video' })
  @ApiResponse({ status: 200, type: VideoDto })
  @ApiBadRequestResponse({ description: 'Invalid UR Play ID' })
  @ApiResponse({ status: 404, description: 'UR Play video not found' })
  @ApiResponse({ status: 502, description: 'Error fetching UR Play data' })
  async getUrVideo(@Param('urId') urId: string): Promise<VideoDto> {
    return this.trainingListService.fetchUrVideo(urId);
  }
}
