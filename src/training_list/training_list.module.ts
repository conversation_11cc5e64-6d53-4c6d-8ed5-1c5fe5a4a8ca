import { Module } from '@nestjs/common';
import { TrainingListService } from './training_list.service';
import { FirebaseModule } from 'src/firebase/firebase.module';
import { MailModule } from 'src/mail/mail.module';
import { VergenceTestsModule } from 'src/vergenceTest/vergence_tests.module';
import { VergenceTestsService } from 'src/vergenceTest/vergence_tests.service';
import { HttpModule } from '@nestjs/axios';
import { TrainingListController } from './training_list.controller';

@Module({
  imports: [FirebaseModule, MailModule, VergenceTestsModule, HttpModule],
  controllers: [TrainingListController],
  providers: [TrainingListService, VergenceTestsService],
})
export class TraininglistModule {}
