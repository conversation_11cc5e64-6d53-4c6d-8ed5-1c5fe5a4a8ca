// src/training-list/training-list.service.ts
import {
  HttpException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  OnModuleDestroy,
  OnModuleInit,
} from '@nestjs/common';
import { CreateTrainingListDto } from './dto/create-training-list.dto';
import { DatabaseService } from 'src/database/database.service';
import { firstValueFrom } from 'rxjs';
import { VideoResponseDto } from './dto/video-response.dto';
import { HttpService } from '@nestjs/axios';
import { UpdateTrainingItemDto } from './dto/update-training-item.dto';
import { CreateTrainingItemDto } from './dto/create-training-item.dto';
import { VideoDto } from './dto/video.dto';
import axios from 'axios';
import * as cheerio from 'cheerio';
import * as puppeteer from 'puppeteer';
import { TrainingListWithMetaDto } from './dto/training-list-with-meta.dto';
import { UpdateTrainingListDto } from './dto/update-training-list.dto';
import { HttpsProxyAgent } from 'https-proxy-agent';
import type { HttpsProxyAgentOptions } from 'https-proxy-agent';

function formatISODuration(iso: string): string {
  const m = iso.match(/^PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?$/);
  if (!m) return '';
  // parse the captured groups or default to 0
  let hours = parseInt(m[1] || '0', 10);
  let mins = parseInt(m[2] || '0', 10);
  let secs = parseInt(m[3] || '0', 10);

  // if there were no explicit minutes but seconds >= 60, roll into minutes
  if (!m[2] && secs >= 60) {
    mins += Math.floor(secs / 60);
    secs %= 60;
  }
  // if minutes overflow, roll into hours
  if (mins >= 60) {
    hours += Math.floor(mins / 60);
    mins %= 60;
  }

  const parts: string[] = [];
  if (hours) parts.push(hours.toString());
  // always pad minutes and seconds to two digits when hours are present
  parts.push(hours ? String(mins).padStart(2, '0') : String(mins));
  parts.push(String(secs).padStart(2, '0'));

  return parts.join(':');
}

@Injectable()
export class TrainingListService implements OnModuleInit, OnModuleDestroy {
  private browser!: puppeteer.Browser;
  private urPage!: puppeteer.Page;

  private readonly UA =
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) ' +
    'AppleWebKit/537.36 (KHTML, like Gecko) ' +
    'Chrome/115.0.0.0 Safari/537.36';
  private httpsAgent!: HttpsProxyAgent<string>;
  constructor(
    private database: DatabaseService,
    private readonly http: HttpService,
  ) {}

  async onModuleInit() {
    const proxy = process.env.PROXY_URL;
    this.httpsAgent = new HttpsProxyAgent<string>(proxy);

    this.browser = await puppeteer.launch({
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        `--proxy-server=${proxy}`,
      ],
    });

    this.urPage = await this.browser.newPage();
    await this.urPage.setUserAgent(this.UA);
    this.httpsAgent = new HttpsProxyAgent(proxy); // <-- Setup Axios proxy here
    // block images/fonts/stylesheets to speed up nav
    await this.urPage.setRequestInterception(true);
    this.urPage.on('request', (req) => {
      const t = req.resourceType();
      if (['image', 'stylesheet', 'font', 'media'].includes(t)) {
        req.abort();
      } else {
        req.continue();
      }
    });

    console.log('[UR] Puppeteer warmed up with proxy & interception enabled');
  }

  async onModuleDestroy() {
    await this.browser.close();
  }
  private formatSeconds(sec: number): string {
    const h = Math.floor(sec / 3600);
    const m = Math.floor((sec % 3600) / 60);
    const s = sec % 60;
    const parts: string[] = [];
    if (h) parts.push(String(h));
    parts.push(h ? String(m).padStart(2, '0') : String(m));
    parts.push(String(s).padStart(2, '0'));
    return parts.join(':');
  }

  async create(dto: CreateTrainingListDto): Promise<any> {
    // 1) Find the teacher's numeric ID by their UUID
    const teacher = await this.database.teacher.findUnique({
      where: { teacher_uuid: dto.teacherUuid },
    });
    if (!teacher) {
      throw new NotFoundException(
        `Teacher with UUID ${dto.teacherUuid} not found`,
      );
    }

    // 2) (Optional) Verify the school class exists
    const schoolClass = await this.database.schoolClass.findUnique({
      where: { id: dto.schoolClassId },
    });
    if (!schoolClass) {
      throw new NotFoundException(`SchoolClass ${dto.schoolClassId} not found`);
    }

    // 3) Create the training list, connecting by the numeric teacher.id
    return this.database.trainingList.create({
      data: {
        name: dto.name,
        isPublic: dto.isPublic,
        teacher: { connect: { id: teacher.id } },
        schoolClass: { connect: { id: dto.schoolClassId } },
      },
    });
  }
  async findAllByTeacherUuid(
    teacherUuid: string,
  ): Promise<TrainingListWithMetaDto[]> {
    // 1) Find the teacher
    const teacher = await this.database.teacher.findUnique({
      where: { teacher_uuid: teacherUuid },
    });
    if (!teacher) {
      throw new NotFoundException(`Teacher with UUID ${teacherUuid} not found`);
    }

    // 2) Fetch lists, include the related class name and all item durations
    const lists = await this.database.trainingList.findMany({
      where: { teacherId: teacher.id },
      orderBy: { createdAt: 'desc' },
      include: {
        // join schoolClass to get its name
        schoolClass: { select: { name: true } },
        // join the items relation (Prisma calls it "items" by default)
        items: { select: { duration_seconds: true } },
      },
    });

    // 3) Map each list into our DTO, summing up durations
    return lists.map((l) => {
      const totalDurationSeconds = l.items.reduce(
        (sum, item) => sum + (item.duration_seconds ?? 0),
        0,
      );
      return {
        id: l.id,
        title: l.name,
        isPublic: l.isPublic,
        isActive: l.isActive,
        teacherId: teacher.id,
        schoolClassId: l.schoolClassId,
        className: l.schoolClass.name,
        itemCount: l.items.length,
        totalDurationSeconds,
        createdAt: l.createdAt,
        updatedAt: l.updatedAt,
      };
    });
  }
  async activateList(listId: number): Promise<void> {
    // 1) fetch the list to get its teacherId
    const list = await this.database.trainingList.findUnique({
      where: { id: listId },
    });
    if (!list) throw new NotFoundException(`List ${listId} not found`);

    // 2) in a transaction: deactivate all, then activate this one
    await this.database.$transaction([
      this.database.trainingList.updateMany({
        where: { teacherId: list.teacherId },
        data: { isActive: false },
      }),
      this.database.trainingList.update({
        where: { id: listId },
        data: { isActive: true },
      }),
    ]);
  }
  async findClassesByTeacherUuid(
    teacherUuid: string,
  ): Promise<Array<{ id: number; name: string }>> {
    // 1) Lookup the teacher's numeric ID by UUID
    const teacher = await this.database.teacher.findUnique({
      where: { teacher_uuid: teacherUuid },
    });
    if (!teacher) {
      throw new NotFoundException(`Teacher with UUID ${teacherUuid} not found`);
    }

    // 2) Get all student-teacher links for that teacher
    const links = await this.database.studentTeacher.findMany({
      where: { teacherId: teacher.id },
      select: { studentId: true },
    });
    if (links.length === 0) {
      return [];
    }
    const studentIds = links.map((l) => l.studentId);

    // 3) Find those students and extract their classId
    const students = await this.database.student.findMany({
      where: { id: { in: studentIds } },
      select: { classId: true },
    });
    const classIds = Array.from(new Set(students.map((s) => s.classId)));
    if (classIds.length === 0) {
      return [];
    }

    // 4) Fetch the unique classes
    const classes = await this.database.schoolClass.findMany({
      where: { id: { in: classIds } },
      select: { id: true, name: true },
    });

    return classes;
  }

  async fetchYouTubeVideo(videoId: string): Promise<VideoResponseDto> {
    const API_KEY = process.env.YOUTUBE_API_KEY;
    if (!API_KEY) {
      throw new InternalServerErrorException('YouTube API key not configured');
    }

    const url = `https://www.googleapis.com/youtube/v3/videos`;
    const params = new URLSearchParams({
      part: 'snippet,contentDetails,statistics',
      id: videoId,
      key: API_KEY,
    }).toString();

    let resp;
    try {
      resp = await firstValueFrom(this.http.get(`${url}?${params}`));
    } catch (err) {
      throw new InternalServerErrorException('Failed to contact YouTube API');
    }

    const data = resp.data;
    if (!data.items || data.items.length === 0) {
      throw new NotFoundException(`Video ${videoId} not found`);
    }
    const item = data.items[0];

    // helper to parse ISO8601 duration
    const parseDuration = (dur: string) => {
      const m = dur.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
      if (!m) return '';
      const [, h = '0', mnt = '0', s = '0'] = m;
      const hours = parseInt(h),
        mins = parseInt(mnt),
        secs = parseInt(s);
      if (hours)
        return `${hours}:${mnt.padStart(2, '0')}:${s.padStart(2, '0')}`;
      return `${mins}:${s.padStart(2, '0')}`;
    };

    // helper to format view count
    const formatViews = (v: string) => {
      const n = parseInt(v);
      if (n >= 1e6) return `${(n / 1e6).toFixed(1)}M views`;
      if (n >= 1e3) return `${(n / 1e3).toFixed(1)}K views`;
      return `${n} views`;
    };

    // helper to format age
    const formatDate = (d: string) => {
      const then = new Date(d),
        now = new Date();
      const days = Math.floor(
        (now.getTime() - then.getTime()) / (1000 * 60 * 60 * 24),
      );
      if (days === 1) return '1 day ago';
      if (days < 30) return `${days} days ago`;
      if (days < 365) return `${Math.floor(days / 30)} months ago`;
      return `${Math.floor(days / 365)} years ago`;
    };

    const snippet = item.snippet;
    const thumbnails = snippet.thumbnails;
    const thumbUrl =
      thumbnails.maxres?.url ||
      thumbnails.high?.url ||
      thumbnails.medium?.url ||
      `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;

    return {
      id: videoId,
      title: snippet.title,
      channel: snippet.channelTitle,
      duration: parseDuration(item.contentDetails.duration),
      views: formatViews(item.statistics.viewCount || '0'),
      uploadDate: formatDate(snippet.publishedAt),
      thumbnail: thumbUrl,
      url: `https://www.youtube.com/watch?v=${videoId}`,
    };
  }
  /** 1) createItem: auto‐assigns the next orderNo based on how many exist */
  async createItem(listId: number, dto: CreateTrainingItemDto) {
    // ensure list exists
    const list = await this.database.trainingList.findUnique({
      where: { id: listId },
    });
    if (!list) throw new NotFoundException(`List ${listId} not found`);

    // count existing items
    const existingCount = await this.database.trainingItem.count({
      where: { trainingListId: listId },
    });

    // create
    return this.database.trainingItem.create({
      data: {
        trainingListId: listId,
        type: dto.type,
        name: dto.name,
        url: dto.url,
        orderNo: existingCount,
        duration_seconds: dto.durationSeconds ?? 0,
      },
    });
  }

  /** 2) list all items in order */
  async findItemsByList(listId: number) {
    return this.database.trainingItem.findMany({
      where: { trainingListId: listId },
      orderBy: { orderNo: 'asc' },
    });
  }

  /** 3) delete one */
  async deleteItem(itemId: number) {
    await this.database.trainingItem.delete({ where: { id: itemId } });
  }

  /** 4) update order only */
  async updateItemOrder(itemId: number, orderNo: number) {
    return this.database.trainingItem.update({
      where: { id: itemId },
      data: { orderNo },
    });
  }

  // NEW: fetch SVT Play page and scrape OG tags
  async fetchSvtVideo(svtId: string): Promise<VideoDto> {
    // 1) call the JSON API for title & duration
    let apiData: any;
    try {
      ({ data: apiData } = await axios.get(
        `https://api.svt.se/videoplayer-api/video/${svtId}`,
      ));
    } catch {
      throw new HttpException(
        'Could not fetch SVT Play API',
        HttpStatus.BAD_GATEWAY,
      );
    }

    const { programTitle, episodeTitle, contentDuration } = apiData;

    let ogImage = '';
    try {
      const html = (await axios.get(`https://www.svtplay.se/video/${svtId}`))
        .data;
      const $ = cheerio.load(html);
      ogImage = $('meta[property="og:image"]').attr('content') || '';
    } catch {}

    const title = episodeTitle
      ? `${programTitle} – ${episodeTitle}`
      : programTitle;

    const duration =
      typeof contentDuration === 'number'
        ? this.formatSeconds(contentDuration)
        : '';

    return {
      id: svtId,
      title,
      channel: 'SVT Play',
      duration,
      views: '',
      uploadDate: '',
      thumbnail: ogImage,
      url: `https://www.svtplay.se/video/${svtId}`,
    };
  }

  async fetchUrVideo(urId: string): Promise<VideoDto> {
    const episodeUrl = `https://urplay.se/avsnitt/${urId}`;
    const seriesUrl = `https://urplay.se/serie/${urId}`;
    let pageUrl = episodeUrl;
    let response: puppeteer.HTTPResponse | null = null;

    // launch a fresh page
    const page = await this.browser.newPage();
    await page.setUserAgent(this.UA);
    await page.setExtraHTTPHeaders({
      'Accept-Language': 'sv-SE,sv;q=0.9,en;q=0.8',
    });

    // intercept heavy resources
    await page.setRequestInterception(true);
    page.on('request', (r) => {
      const t = r.resourceType();
      if (['image', 'stylesheet', 'font', 'media'].includes(t)) r.abort();
      else r.continue();
    });

    // 1) Try /avsnitt/, fall back to /serie/
    try {
      response = await page.goto(pageUrl, {
        waitUntil: 'domcontentloaded',
        timeout: 60000,
      });
      const st = response?.status() ?? 0;
      if (st === 403 || st === 404) throw new Error(`HTTP ${st}`);
    } catch {
      pageUrl = seriesUrl;
      response = await page.goto(pageUrl, {
        waitUntil: 'domcontentloaded',
        timeout: 60000,
      });
      const st2 = response?.status() ?? 0;
      if (st2 === 403 || st2 === 404) {
        await page.close();
        throw new NotFoundException(`UR Play video ${urId} not found`);
      }
    }

    // 2) Log out the first 500 chars of the HTML so we can inspect it
    const html = await page.content();
    console.log(
      `[UR] page HTML snippet for ${pageUrl}:\n${html
        .slice(0, 500)
        .replace(/\n/g, ' ')}`,
    );

    // 3) Attempt to read <meta> tags
    let title = '';
    let thumbnail = '';
    try {
      title = await page.$eval(
        'meta[property="og:title"]',
        (el) => (el as HTMLMetaElement).content || '',
      );
      thumbnail = await page.$eval(
        'meta[property="og:image"]',
        (el) => (el as HTMLMetaElement).content || '',
      );
    } catch (e) {
      console.error(`[UR] meta tag scrape failed:`, e);
    }

    if (!title) {
      await page.close();
      // We now have the raw HTML—check your logs to see why title is missing
      throw new NotFoundException(`UR Play video ${urId} not found`);
    }

    // 4) Scrape duration from visible text
    const bodyText: string = await page.evaluate(() => document.body.innerText);
    let duration = '';
    let m = bodyText.match(/\b(\d{1,2}):(\d{2})\b/);
    if (m) duration = `${m[1]}:${m[2]}`;
    else {
      m = bodyText.match(/(\d+)\s*min(?:ut(?:er)?)?\s*[,;]?\s*(\d+)\s*sek/i);
      if (m) duration = `${m[1]}:${m[2].padStart(2, '0')}`;
      else {
        m = bodyText.match(/·\s*(\d+)\s*min/);
        if (m) duration = `${m[1]}:00`;
      }
    }

    await page.close();

    return {
      id: urId,
      title,
      channel: 'UR Play',
      duration,
      views: '',
      uploadDate: '',
      thumbnail,
      url: pageUrl,
    };
  }
  async updateList(listId: number, dto: UpdateTrainingListDto) {
    const existing = await this.database.trainingList.findUnique({
      where: { id: listId },
    });
    if (!existing) throw new NotFoundException(`List ${listId} not found`);
    return this.database.trainingList.update({
      where: { id: listId },
      data: {
        ...(dto.name !== undefined && { name: dto.name }),
        ...(dto.isPublic !== undefined && { isPublic: dto.isPublic }),
        ...(dto.schoolClassId !== undefined && {
          schoolClassId: dto.schoolClassId,
        }),
      },
    });
  }

  async deleteList(listId: number) {
    const existing = await this.database.trainingList.findUnique({
      where: { id: listId },
    });
    if (!existing) throw new NotFoundException(`List ${listId} not found`);
    // DB cascade removes items
    await this.database.trainingList.delete({ where: { id: listId } });
  }
}
