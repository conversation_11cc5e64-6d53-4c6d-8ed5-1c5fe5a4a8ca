import { Logger } from '@nestjs/common';

export interface RetryOptions {
  maxRetries?: number;
  baseDelay?: number;
  maxDelay?: number;
  retryCondition?: (error: any) => boolean;
  onRetry?: (error: any, attempt: number) => void;
}

export class RetryUtil {
  private static readonly logger = new Logger(RetryUtil.name);

  /**
   * Generic retry mechanism with exponential backoff
   */
  static async withRetry<T>(
    operation: () => Promise<T>,
    options: RetryOptions = {},
  ): Promise<T> {
    const {
      maxRetries = 3,
      baseDelay = 1000,
      maxDelay = 30000,
      retryCondition = RetryUtil.defaultRetryCondition,
      onRetry,
    } = options;

    let lastError: any;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        const isLastAttempt = attempt === maxRetries;

        if (isLastAttempt || !retryCondition(error)) {
          throw error;
        }

        const delay = Math.min(baseDelay * Math.pow(2, attempt - 1), maxDelay);

        if (onRetry) {
          onRetry(error, attempt);
        } else {
          RetryUtil.logger.warn(
            `Attempt ${attempt}/${maxRetries} failed: ${error.message}. Retrying in ${delay}ms`,
          );
        }

        await RetryUtil.sleep(delay);
      }
    }

    throw lastError;
  }

  /**
   * Retry mechanism specifically for fetch requests
   */
  static async fetchWithRetry(
    url: string,
    options: RequestInit = {},
    retryOptions: RetryOptions = {},
  ): Promise<Response> {
    return RetryUtil.withRetry(
      async () => {
        const response = await fetch(url, {
          ...options,
          signal: options.signal || AbortSignal.timeout(10000),
        });

        // Handle rate limiting
        if (response.status === 429) {
          const retryAfter = response.headers.get('Retry-After');
          const delay = retryAfter ? parseInt(retryAfter) * 1000 : 1000;

          const error = new Error(`Rate limited (429). Retry after ${delay}ms`);
          (error as any).isRateLimited = true;
          (error as any).retryAfter = delay;
          throw error;
        }

        // Handle server errors
        if (response.status >= 500) {
          throw new Error(
            `Server error: ${response.status} ${response.statusText}`,
          );
        }

        // Handle client errors (don't retry these)
        if (response.status >= 400 && response.status < 500) {
          throw new Error(
            `Client error: ${response.status} ${response.statusText}`,
          );
        }

        if (!response.ok) {
          throw new Error(
            `HTTP error: ${response.status} ${response.statusText}`,
          );
        }

        return response;
      },
      {
        ...retryOptions,
        retryCondition: (error) => {
          // Custom retry condition for HTTP requests
          if ((error as any).isRateLimited) {
            return true;
          }

          // Retry on network errors, timeouts, and server errors
          return (
            error.name === 'AbortError' ||
            error.name === 'TypeError' ||
            error.message.includes('fetch') ||
            error.message.includes('Server error') ||
            error.message.includes('Rate limited')
          );
        },
        onRetry: (error, attempt) => {
          if ((error as any).isRateLimited) {
            RetryUtil.logger.warn(
              `Rate limited on attempt ${attempt}. Waiting ${
                (error as any).retryAfter
              }ms`,
            );
          } else {
            RetryUtil.logger.warn(
              `HTTP request attempt ${attempt} failed: ${error.message}`,
            );
          }
        },
      },
    );
  }

  /**
   * Default retry condition - retries on network errors and timeouts
   */
  private static defaultRetryCondition(error: any): boolean {
    return (
      error.name === 'AbortError' ||
      error.name === 'TypeError' ||
      error.code === 'ECONNRESET' ||
      error.code === 'ENOTFOUND' ||
      error.code === 'ECONNREFUSED' ||
      error.message.includes('timeout') ||
      error.message.includes('network')
    );
  }

  /**
   * Sleep utility for retry delays
   */
  private static sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
