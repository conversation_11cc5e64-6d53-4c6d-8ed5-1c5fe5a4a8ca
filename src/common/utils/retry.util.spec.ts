import { RetryUtil } from './retry.util';

// Mock fetch globally
global.fetch = jest.fn();

describe('RetryUtil', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('withRetry', () => {
    it('should succeed on first attempt', async () => {
      const mockOperation = jest.fn().mockResolvedValue('success');

      const result = await RetryUtil.withRetry(mockOperation);

      expect(result).toBe('success');
      expect(mockOperation).toHaveBeenCalledTimes(1);
    });

    it('should retry on failure and eventually succeed', async () => {
      const networkError = new TypeError('Network error');
      const mockOperation = jest
        .fn()
        .mockRejectedValueOnce(networkError)
        .mockRejectedValueOnce(networkError)
        .mockResolvedValue('success');

      const result = await RetryUtil.withRetry(mockOperation, {
        maxRetries: 3,
        baseDelay: 10, // Use small delay for testing
      });

      expect(result).toBe('success');
      expect(mockOperation).toHaveBeenCalledTimes(3);
    });

    it('should fail after max retries', async () => {
      const networkError = new TypeError('Network timeout');
      const mockOperation = jest.fn().mockRejectedValue(networkError);

      await expect(
        RetryUtil.withRetry(mockOperation, {
          maxRetries: 2,
          baseDelay: 10,
        }),
      ).rejects.toThrow('Network timeout');

      expect(mockOperation).toHaveBeenCalledTimes(2);
    });

    it('should not retry when retryCondition returns false', async () => {
      const mockOperation = jest.fn().mockRejectedValue(new Error('Client error'));

      await expect(
        RetryUtil.withRetry(mockOperation, {
          maxRetries: 3,
          retryCondition: () => false,
        }),
      ).rejects.toThrow('Client error');

      expect(mockOperation).toHaveBeenCalledTimes(1);
    });
  });

  describe('fetchWithRetry', () => {
    it('should succeed on first attempt', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValue({ data: 'test' }),
      };
      (fetch as jest.Mock).mockResolvedValue(mockResponse);

      const result = await RetryUtil.fetchWithRetry('https://api.example.com/test');

      expect(result).toBe(mockResponse);
      expect(fetch).toHaveBeenCalledTimes(1);
    });

    it('should retry on 500 server error', async () => {
      const mockErrorResponse = {
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
      };
      const mockSuccessResponse = {
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValue({ data: 'test' }),
      };

      (fetch as jest.Mock)
        .mockResolvedValueOnce(mockErrorResponse)
        .mockResolvedValue(mockSuccessResponse);

      const result = await RetryUtil.fetchWithRetry('https://api.example.com/test', {}, {
        maxRetries: 2,
        baseDelay: 10, // Use small delay for testing
      });

      expect(result).toBe(mockSuccessResponse);
      expect(fetch).toHaveBeenCalledTimes(2);
    });

    it('should handle rate limiting (429)', async () => {
      const mockRateLimitResponse = {
        ok: false,
        status: 429,
        statusText: 'Too Many Requests',
        headers: {
          get: jest.fn().mockReturnValue('1'), // Retry-After: 1 second
        },
      };
      const mockSuccessResponse = {
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValue({ data: 'test' }),
      };

      (fetch as jest.Mock)
        .mockResolvedValueOnce(mockRateLimitResponse)
        .mockResolvedValue(mockSuccessResponse);

      const result = await RetryUtil.fetchWithRetry('https://api.example.com/test', {}, {
        maxRetries: 2,
        baseDelay: 10,
      });

      expect(result).toBe(mockSuccessResponse);
      expect(fetch).toHaveBeenCalledTimes(2);
    });

    it('should not retry on 400 client error', async () => {
      const mockErrorResponse = {
        ok: false,
        status: 400,
        statusText: 'Bad Request',
      };

      (fetch as jest.Mock).mockResolvedValue(mockErrorResponse);

      await expect(
        RetryUtil.fetchWithRetry('https://api.example.com/test'),
      ).rejects.toThrow('Client error: 400 Bad Request');

      expect(fetch).toHaveBeenCalledTimes(1);
    });

    it('should retry on network error', async () => {
      const networkError = new TypeError('Failed to fetch');
      const mockSuccessResponse = {
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValue({ data: 'test' }),
      };

      (fetch as jest.Mock)
        .mockRejectedValueOnce(networkError)
        .mockResolvedValue(mockSuccessResponse);

      const result = await RetryUtil.fetchWithRetry('https://api.example.com/test', {}, {
        maxRetries: 2,
        baseDelay: 10,
      });

      expect(result).toBe(mockSuccessResponse);
      expect(fetch).toHaveBeenCalledTimes(2);
    });
  });
});
