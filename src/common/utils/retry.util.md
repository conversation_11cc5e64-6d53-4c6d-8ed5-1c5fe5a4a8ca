# Retry Utility

A robust retry mechanism with exponential backoff for handling transient failures in external API calls.

## Features

- **Exponential backoff**: Delays increase exponentially between retries
- **Configurable retry conditions**: Customize which errors should trigger retries
- **Rate limiting support**: Handles HTTP 429 responses with Retry-After headers
- **Network error handling**: Automatically retries on network timeouts and connection errors
- **Logging**: Built-in logging for retry attempts and failures

## Usage

### Basic Retry with Custom Operation

```typescript
import { RetryUtil } from '../common/utils/retry.util';

// Retry any async operation
const result = await RetryUtil.withRetry(
  async () => {
    // Your operation here
    const data = await someApiCall();
    return data;
  },
  {
    maxRetries: 3,
    baseDelay: 1000, // 1 second
    maxDelay: 30000, // 30 seconds max
  }
);
```

### HTTP Requests with Fetch

```typescript
import { RetryUtil } from '../common/utils/retry.util';

// For simple fetch requests
const response = await RetryUtil.fetchWithRetry(
  'https://api.example.com/data',
  {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer token',
    },
    body: JSON.stringify({ data: 'example' }),
  },
  {
    maxRetries: 3,
    baseDelay: 1000,
  }
);

const data = await response.json();
```

### Custom Retry Conditions

```typescript
import { RetryUtil } from '../common/utils/retry.util';

const result = await RetryUtil.withRetry(
  async () => {
    // Your operation
    return await apiCall();
  },
  {
    maxRetries: 5,
    baseDelay: 500,
    retryCondition: (error) => {
      // Only retry on specific error codes
      return error.code === 'ECONNRESET' || 
             error.code === 'ETIMEDOUT' ||
             error.message.includes('timeout');
    },
    onRetry: (error, attempt) => {
      console.log(`Retry attempt ${attempt}: ${error.message}`);
    },
  }
);
```

### Integration with Axios

```typescript
import axios from 'axios';
import { RetryUtil } from '../common/utils/retry.util';

const data = await RetryUtil.withRetry(
  async () => {
    const response = await axios.get('https://api.example.com/data', {
      timeout: 10000,
    });
    return response.data;
  },
  {
    maxRetries: 3,
    baseDelay: 1000,
    retryCondition: (error) => {
      // Retry on network errors and 5xx responses
      return axios.isAxiosError(error) && (
        !error.response || 
        error.response.status >= 500 ||
        error.code === 'ECONNABORTED'
      );
    },
  }
);
```

### Integration with NestJS HttpService

```typescript
import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { RetryUtil } from '../common/utils/retry.util';

@Injectable()
export class ExampleService {
  constructor(private readonly httpService: HttpService) {}

  async fetchData() {
    return RetryUtil.withRetry(
      async () => {
        const response = await firstValueFrom(
          this.httpService.get('https://api.example.com/data')
        );
        return response.data;
      },
      {
        maxRetries: 3,
        baseDelay: 1000,
        onRetry: (error, attempt) => {
          console.log(`API call retry attempt ${attempt}: ${error.message}`);
        },
      }
    );
  }
}
```

## Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `maxRetries` | number | 3 | Maximum number of retry attempts |
| `baseDelay` | number | 1000 | Base delay in milliseconds |
| `maxDelay` | number | 30000 | Maximum delay between retries |
| `retryCondition` | function | Built-in | Function to determine if error should trigger retry |
| `onRetry` | function | Built-in logging | Callback called on each retry attempt |

## Default Retry Conditions

The utility automatically retries on:

- **Network errors**: `TypeError`, `ECONNRESET`, `ENOTFOUND`, `ECONNREFUSED`
- **Timeout errors**: `AbortError`, timeout-related messages
- **HTTP 429**: Rate limiting (respects Retry-After header)
- **HTTP 5xx**: Server errors

It does **NOT** retry on:

- **HTTP 4xx** (except 429): Client errors like 400, 401, 403, 404
- **Custom business logic errors**: Unless specified in retryCondition

## Best Practices

1. **Use appropriate timeouts**: Always set reasonable timeouts for your requests
2. **Configure max delays**: Prevent excessive waiting with `maxDelay`
3. **Custom retry conditions**: Tailor retry logic to your specific API requirements
4. **Logging**: Use `onRetry` callback for monitoring and debugging
5. **Circuit breaker pattern**: Consider implementing circuit breakers for frequently failing services

## Examples in the Codebase

- **Shopify API**: `src/purchases/purchases.service.ts` - Metafield updates with rate limiting support
- **External APIs**: Can be applied to E3PL, WooCommerce, YouTube, SVT, Binogi, and Loco API calls
