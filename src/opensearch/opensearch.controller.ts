import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  DefaultValuePipe,
  HttpCode,
  HttpStatus,
  ParseIntPipe,
} from '@nestjs/common';
import { InjectOpensearchClient, OpensearchClient } from 'nestjs-opensearch';

interface LogEntry {
  key: string;
  level: string;
  message: string;
  meta?: Record<string, any>;
  '@timestamp'?: string;
  _uniqueId?: string;
}

interface GroupedLogEntry {
  key: string;
  count: number;
  latest_timestamp: string;
  recent_logs: LogEntry[];
}

interface GroupedSearchResponse {
  groups: GroupedLogEntry[];
  total_groups: number;
  from: number;
  size: number;
}

@Controller('logs')
export class LogsController {
  constructor(
    @InjectOpensearchClient() private readonly os: OpensearchClient,
  ) {}

  private async searchGroupedByKey(
    queryClause: any,
    from: number,
    size: number,
    recent_logs: number = 10,
  ): Promise<GroupedSearchResponse> {
    const { body } = await this.os.search({
      index: 'app-logs-*',
      size: 0, // We don't need individual hits, only aggregations
      body: {
        query: queryClause,
        aggs: {
          grouped_by_key: {
            terms: {
              field: 'key.keyword',
              size: 10000, // Large enough to capture all unique keys
              order: { latest_timestamp: 'desc' },
            },
            aggs: {
              latest_timestamp: {
                max: { field: '@timestamp' },
              },
              recent_logs: {
                top_hits: {
                  size: Math.min(recent_logs, 100), // Number of recent logs per key
                  sort: [{ '@timestamp': { order: 'desc' } }],
                  _source: {
                    includes: ['key', 'level', 'message', 'meta', '@timestamp'],
                  },
                },
              },
            },
          },
        },
      },
    });

    const buckets = (body.aggregations?.grouped_by_key as any)?.buckets || [];
    const total_groups = buckets.length;

    // Apply pagination to groups
    const paginatedBuckets = buckets.slice(from, from + size);

    const groups: GroupedLogEntry[] = paginatedBuckets.map((bucket: any) => {
      return {
        key: bucket.key,
        count: bucket.doc_count,
        latest_timestamp: bucket.latest_timestamp.value_as_string,
        recent_logs: bucket.recent_logs.hits.hits.map(
          (hit: any, index: number) => ({
            key: hit._source.key,
            level: hit._source.level,
            message: hit._source.message,
            meta: hit._source.meta || {},
            '@timestamp': hit._source['@timestamp'],
            _uniqueId: `${hit._id}-recent-${index}-${Date.now()}`,
          }),
        ),
      };
    });

    return {
      groups,
      total_groups,
      from,
      size,
    };
  }

  @Post('bulk')
  @HttpCode(200)
  async bulkCreate(@Body() batch: LogEntry[]) {
    const indexName = `app-logs-${new Date().toISOString().slice(0, 10)}`;

    // Build a single bulk request
    const body = batch.flatMap((entry) => [
      { index: { _index: indexName } },
      {
        '@timestamp': new Date().toISOString(),
        key: entry.key,
        level: entry.level,
        message: entry.message,
        meta: entry.meta || {},
      },
    ]);

    const { body: bulkResp } = await this.os.bulk({ refresh: true, body });
    if (bulkResp.errors) {
      const errors = bulkResp.items
        .map((item: any) => item.index?.error)
        .filter((e: any) => !!e);
      return { success: false, errors };
    }
    return { success: true, count: batch.length };
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(
    @Body('key') key: string,
    @Body('level') level: string,
    @Body('message') message: string,
    @Body('meta', new DefaultValuePipe({})) meta: Record<string, any>,
  ) {
    const indexName = `app-logs-${new Date().toISOString().slice(0, 10)}`;
    const now = new Date().toISOString();

    const resp = await this.os.index({
      index: indexName,
      body: {
        '@timestamp': now,
        key,
        level,
        message,
        meta,
      },
    });

    await this.os.indices.refresh({ index: indexName });

    return {
      result: resp.body.result,
      _id: resp.body._id,
      index: resp.body._index,
    };
  }

  @Get()
  async search(
    @Query('q') q?: string,
    @Query('key') userKey?: string,
    @Query('level') level?: string,
    @Query('groupBy') groupBy?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('from', new DefaultValuePipe(0), ParseIntPipe) from: number = 0,
    @Query('size', new DefaultValuePipe(100), ParseIntPipe) size: number = 100,
    @Query('recent_logs', new DefaultValuePipe(10), ParseIntPipe)
    recent_logs: number = 10,
  ) {
    const must: any[] = [];

    // 1) Full‐text / multi‐field search when the 'filter' box (q) is used:
    if (q) {
      must.push({
        multi_match: {
          query: q,
          fields: [
            'message', // search inside message text
            'meta.data', // search inside meta.data
            'key.keyword', // exact match on key
            'user.keyword',
          ],
          type: 'best_fields',
          operator: 'and',
        },
      });
    }

    // 2) Exact‐match filter on "level"
    if (level) {
      must.push({ term: { 'level.keyword': level } });
    }

    // 3) Explicit userKey filter (OR between key.keyword and user.keyword)
    if (userKey) {
      must.push({
        bool: {
          should: [
            { term: { 'key.keyword': userKey } },
            { term: { 'user.keyword': userKey } },
          ],
          minimum_should_match: 1,
        },
      });
    }

    // 4) Date range filter
    if (startDate || endDate) {
      const rangeQuery: any = {};
      if (startDate) {
        // Handle datetime-local format (YYYY-MM-DDTHH:MM)
        // This assumes the input is in the user's local timezone
        let startDateISO: string;
        if (startDate.includes('T')) {
          // If it's already in datetime-local format, convert to ISO
          startDateISO = new Date(startDate).toISOString();
        } else {
          // If it's already an ISO string, use as-is
          startDateISO = startDate;
        }
        rangeQuery.gte = startDateISO;
      }
      if (endDate) {
        // Handle datetime-local format (YYYY-MM-DDTHH:MM)
        let endDateISO: string;
        if (endDate.includes('T') && !endDate.includes('Z')) {
          // If it's in datetime-local format, convert to ISO
          endDateISO = new Date(endDate).toISOString();
        } else {
          // If it's already an ISO string, use as-is
          endDateISO = endDate;
        }
        rangeQuery.lte = endDateISO;
      }
      must.push({
        range: {
          '@timestamp': rangeQuery,
        },
      });
    }

    // If nothing was added to must[], use a match_all
    const queryClause = must.length ? { bool: { must } } : { match_all: {} };

    // Handle grouped search
    if (groupBy === 'key') {
      return this.searchGroupedByKey(queryClause, from, size, recent_logs);
    }

    const { body } = await this.os.search({
      index: 'app-logs-*',
      from,
      size,
      body: {
        query: queryClause,
        sort: [{ '@timestamp': { order: 'desc' } }],
        track_total_hits: true,
      },
    });

    const hits = (body.hits.hits as any[]).map((h, index) => ({
      ...h._source,
      _uniqueId: `${h._id}-${from + index}-${Date.now()}`,
    }));
    const totalRaw = body.hits.total;
    const total = typeof totalRaw === 'number' ? totalRaw : totalRaw.value;

    return { items: hits, total, from, size };
  }
}
